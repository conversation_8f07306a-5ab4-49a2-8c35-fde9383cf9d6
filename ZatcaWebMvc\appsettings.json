{"ConnectionStrings": {"DefaultConnection": "Server=.;Database=ZatcaWebMvcIdentity;Integrated Security=true;Trusted_Connection=True;MultipleActiveResultSets=true;TrustServerCertificate=true;Encrypt=false"}, "ZatcaApi": {"BaseUrl": "https://localhost:7180", "ApiKey": "", "TimeoutSeconds": 30, "RetryAttempts": 3, "RetryDelaySeconds": 2}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*"}