﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - ZATCA E-Invoice Management</title>

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Modern Design System -->
    <link rel="stylesheet" href="~/css/modern-design-system.css" asp-append-version="true" />
    <!-- Custom CSS -->
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/ZatcaWebMvc.styles.css" asp-append-version="true" />
</head>
<body class="d-flex flex-column min-vh-100">
    <!-- Modern Navigation -->
    <header>
        <nav class="modern-navbar">
            <div class="modern-navbar-container">
                <!-- Brand -->
                <a class="modern-navbar-brand" asp-area="" asp-controller="Home" asp-action="Index">
                    <i class="bi bi-receipt"></i>
                    <span class="d-none d-md-inline">ZATCA E-Invoice</span>
                    <span class="d-md-none">ZATCA</span>
                </a>

                <!-- Mobile Toggle -->
                <button class="d-lg-none modern-btn modern-btn-secondary modern-btn-sm" type="button" data-bs-toggle="offcanvas" data-bs-target="#sidebarOffcanvas" aria-controls="sidebarOffcanvas">
                    <i class="bi bi-list"></i>
                </button>

                <!-- Desktop Navigation -->
                <div class="d-none d-lg-flex align-items-center gap-4">
                    <ul class="modern-navbar-nav">
                        <li>
                            <a class="modern-navbar-link" asp-area="" asp-controller="Home" asp-action="Index">
                                <i class="bi bi-speedometer2 me-1"></i>Dashboard
                            </a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="modern-navbar-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-file-earmark-text me-1"></i>Invoices
                            </a>
                            <ul class="dropdown-menu shadow border-0 mt-2">
                                <li><a class="dropdown-item" asp-controller="Invoice" asp-action="Index">
                                    <i class="bi bi-list me-2 text-primary"></i>View All Invoices
                                </a></li>
                                <li><a class="dropdown-item" asp-controller="Invoice" asp-action="Create">
                                    <i class="bi bi-plus-circle me-2 text-success"></i>Create New Invoice
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" asp-controller="Invoice" asp-action="Pending">
                                    <i class="bi bi-clock me-2 text-warning"></i>Pending Invoices
                                </a></li>
                            </ul>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="modern-navbar-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-shield-check me-1"></i>Certificates
                            </a>
                            <ul class="dropdown-menu shadow border-0 mt-2">
                                <li><a class="dropdown-item" asp-controller="Certificate" asp-action="Index">
                                    <i class="bi bi-list me-2 text-primary"></i>View All Certificates
                                </a></li>
                                <li><a class="dropdown-item" asp-controller="Certificate" asp-action="Create">
                                    <i class="bi bi-plus-circle me-2 text-success"></i>Create New Certificate
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" asp-controller="Certificate" asp-action="Expired">
                                    <i class="bi bi-exclamation-triangle me-2 text-warning"></i>Expired Certificates
                                </a></li>
                            </ul>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="modern-navbar-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-building me-1"></i>Sellers
                            </a>
                            <ul class="dropdown-menu shadow border-0 mt-2">
                                <li><a class="dropdown-item" asp-controller="Seller" asp-action="Index">
                                    <i class="bi bi-list me-2 text-primary"></i>View All Sellers
                                </a></li>
                                <li><a class="dropdown-item" asp-controller="Seller" asp-action="Create">
                                    <i class="bi bi-plus-circle me-2 text-success"></i>Create New Seller
                                </a></li>
                            </ul>
                        </li>
                        <li>
                            <a class="modern-navbar-link" asp-controller="ApiTest" asp-action="Index">
                                <i class="bi bi-gear me-1"></i>API Test
                            </a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="modern-navbar-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-shield-lock me-1"></i>Admin
                            </a>
                            <ul class="dropdown-menu shadow border-0 mt-2">
                                <li><a class="dropdown-item" asp-controller="Admin" asp-action="Index">
                                    <i class="bi bi-speedometer2 me-2 text-primary"></i>Admin Dashboard
                                </a></li>
                                <li><a class="dropdown-item" asp-controller="Admin" asp-action="Environment">
                                    <i class="bi bi-server me-2 text-success"></i>Environment Management
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" asp-controller="ApiTest" asp-action="Index">
                                    <i class="bi bi-gear me-2 text-info"></i>API Diagnostics
                                </a></li>
                            </ul>
                        </li>
                    </ul>

                    <!-- User Authentication Menu -->
                    @await Html.PartialAsync("_LoginPartial")
                </div>
            </div>
        </nav>
    </header>

    <!-- Mobile Sidebar Offcanvas -->
    <div class="offcanvas offcanvas-start" tabindex="-1" id="sidebarOffcanvas" aria-labelledby="sidebarOffcanvasLabel">
        <div class="offcanvas-header bg-primary text-white">
            <h5 class="offcanvas-title" id="sidebarOffcanvasLabel">
                <i class="bi bi-receipt me-2"></i>ZATCA E-Invoice
            </h5>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="offcanvas" aria-label="Close"></button>
        </div>
        <div class="offcanvas-body p-0">
            <nav class="nav flex-column">
                <a class="nav-link" asp-controller="Home" asp-action="Index">
                    <i class="bi bi-speedometer2 me-2"></i>Dashboard
                </a>

                <div class="nav-section">
                    <h6 class="nav-section-title">Invoices</h6>
                    <a class="nav-link" asp-controller="Invoice" asp-action="Index">
                        <i class="bi bi-list me-2"></i>View All Invoices
                    </a>
                    <a class="nav-link" asp-controller="Invoice" asp-action="Create">
                        <i class="bi bi-plus-circle me-2"></i>Create New Invoice
                    </a>
                    <a class="nav-link" asp-controller="Invoice" asp-action="Pending">
                        <i class="bi bi-clock me-2"></i>Pending Invoices
                    </a>
                </div>

                <div class="nav-section">
                    <h6 class="nav-section-title">Certificates</h6>
                    <a class="nav-link" asp-controller="Certificate" asp-action="Index">
                        <i class="bi bi-list me-2"></i>View All Certificates
                    </a>
                    <a class="nav-link" asp-controller="Certificate" asp-action="Create">
                        <i class="bi bi-plus-circle me-2"></i>Create New Certificate
                    </a>
                    <a class="nav-link" asp-controller="Certificate" asp-action="Expired">
                        <i class="bi bi-exclamation-triangle me-2"></i>Expired Certificates
                    </a>
                </div>

                <div class="nav-section">
                    <h6 class="nav-section-title">Sellers</h6>
                    <a class="nav-link" asp-controller="Seller" asp-action="Index">
                        <i class="bi bi-list me-2"></i>View All Sellers
                    </a>
                    <a class="nav-link" asp-controller="Seller" asp-action="Create">
                        <i class="bi bi-plus-circle me-2"></i>Create New Seller
                    </a>
                </div>

                <div class="nav-section">
                    <h6 class="nav-section-title">Administration</h6>
                    <a class="nav-link" asp-controller="Admin" asp-action="Index">
                        <i class="bi bi-speedometer2 me-2"></i>Admin Dashboard
                    </a>
                    <a class="nav-link" asp-controller="Admin" asp-action="Environment">
                        <i class="bi bi-server me-2"></i>Environment Management
                    </a>
                </div>

                <div class="nav-section">
                    <h6 class="nav-section-title">System</h6>
                    <a class="nav-link" asp-controller="ApiTest" asp-action="Index">
                        <i class="bi bi-gear me-2"></i>API Test
                    </a>
                </div>
            </nav>
        </div>
    </div>

    <!-- Main Content with Modern Layout -->
    <main class="flex-grow-1" style="padding-top: 80px;">
        <!-- Toast Container for Notifications -->
        <div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 1055;">
            <div id="liveToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="toast-header">
                    <i class="bi bi-bell-fill me-2 text-primary"></i>
                    <strong class="me-auto">Notification</strong>
                    <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
                <div class="toast-body"></div>
            </div>
        </div>

        <!-- Modern Alert Messages -->
        <div class="modern-container">
            @if (TempData["SuccessMessage"] != null)
            {
                <div class="modern-alert modern-alert-success mt-4">
                    <div class="modern-alert-icon">
                        <i class="bi bi-check-circle-fill"></i>
                    </div>
                    <div class="modern-alert-content">
                        <div class="modern-alert-title">Success</div>
                        <div class="modern-alert-message">@TempData["SuccessMessage"]</div>
                    </div>
                </div>
            }
            @if (TempData["ErrorMessage"] != null)
            {
                <div class="modern-alert modern-alert-error mt-4">
                    <div class="modern-alert-icon">
                        <i class="bi bi-exclamation-triangle-fill"></i>
                    </div>
                    <div class="modern-alert-content">
                        <div class="modern-alert-title">Error</div>
                        <div class="modern-alert-message">@TempData["ErrorMessage"]</div>
                    </div>
                </div>
            }
            @if (TempData["InfoMessage"] != null)
            {
                <div class="modern-alert modern-alert-info mt-4">
                    <div class="modern-alert-icon">
                        <i class="bi bi-info-circle-fill"></i>
                    </div>
                    <div class="modern-alert-content">
                        <div class="modern-alert-title">Information</div>
                        <div class="modern-alert-message">@TempData["InfoMessage"]</div>
                    </div>
                </div>
            }
        </div>

        <!-- Page Content -->
        <div class="modern-container py-6">
            @RenderBody()
        </div>
    </main>

    <footer class="bg-white border-top mt-auto">
        <div class="modern-container py-6">
            <div class="flex justify-between items-center">
                <div>
                    <p class="text-gray-600 mb-0 text-sm">
                        &copy; @DateTime.Now.Year - ZATCA E-Invoice Management System
                    </p>
                </div>
                <div>
                    <p class="text-gray-600 mb-0 text-sm">
                        <i class="bi bi-envelope me-1"></i>
                        <a href="mailto:<EMAIL>" class="text-primary text-decoration-none"><EMAIL></a>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap 5 JS Bundle -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery (if needed for legacy code) -->
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <!-- Custom JS -->
    <script src="~/js/site.js" asp-append-version="true"></script>

    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
