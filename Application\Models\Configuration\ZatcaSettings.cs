using Domain.Enums;
using System.ComponentModel.DataAnnotations;

namespace Application.Models.Configuration
{
    /// <summary>
    /// Configuration model for ZATCA settings including environment selection and URL mapping
    /// </summary>
    public class ZatcaSettings
    {
        /// <summary>
        /// Configuration section name in appsettings.json
        /// </summary>
        public const string SectionName = "ZatcaSettings";

        /// <summary>
        /// The current environment to use for ZATCA API calls
        /// </summary>
        public ZatcaEnvironment Environment { get; set; } = ZatcaEnvironment.Simulation;

        /// <summary>
        /// URL for the Developer environment
        /// </summary>
        [Required]
        [Url]
        public string DeveloperUrl { get; set; } = "https://gw-fatoora.zatca.gov.sa/e-invoicing/developer-portal/";

        /// <summary>
        /// URL for the Simulation environment
        /// </summary>
        [Required]
        [Url]
        public string SimulationUrl { get; set; } = "https://gw-fatoora.zatca.gov.sa/e-invoicing/simulation/";

        /// <summary>
        /// URL for the Production environment
        /// </summary>
        [Required]
        [Url]
        public string ProductionUrl { get; set; } = "https://gw-fatoora.zatca.gov.sa/e-invoicing/core/";

        /// <summary>
        /// One-Time Password for ZATCA operations
        /// </summary>
        public string Otp { get; set; } = string.Empty;

        /// <summary>
        /// Gets the URL for the currently selected environment
        /// </summary>
        /// <returns>The URL corresponding to the current environment</returns>
        public string GetCurrentEnvironmentUrl()
        {
            return Environment switch
            {
                ZatcaEnvironment.Developer => DeveloperUrl,
                ZatcaEnvironment.Simulation => SimulationUrl,
                ZatcaEnvironment.Production => ProductionUrl,
                _ => SimulationUrl // Default to simulation if unknown
            };
        }

        /// <summary>
        /// Gets the environment name as a string
        /// </summary>
        /// <returns>The environment name</returns>
        public string GetEnvironmentName()
        {
            return Environment.ToString();
        }

        /// <summary>
        /// Validates that all required URLs are properly configured
        /// </summary>
        /// <returns>True if all URLs are valid, false otherwise</returns>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(DeveloperUrl) &&
                   !string.IsNullOrWhiteSpace(SimulationUrl) &&
                   !string.IsNullOrWhiteSpace(ProductionUrl) &&
                   Uri.IsWellFormedUriString(DeveloperUrl, UriKind.Absolute) &&
                   Uri.IsWellFormedUriString(SimulationUrl, UriKind.Absolute) &&
                   Uri.IsWellFormedUriString(ProductionUrl, UriKind.Absolute);
        }

        /// <summary>
        /// Sets the environment and returns the corresponding URL
        /// </summary>
        /// <param name="environment">The environment to set</param>
        /// <returns>The URL for the specified environment</returns>
        public string SetEnvironment(ZatcaEnvironment environment)
        {
            Environment = environment;
            return GetCurrentEnvironmentUrl();
        }
    }
}
