@page
@model RegisterModel
@{
    ViewData["Title"] = "Create Account";
    Layout = "_Layout";
}

<div class="modern-container" style="min-height: 80vh; display: flex; align-items: center; justify-content: center; padding: 2rem 1rem;">
    <div style="width: 100%; max-width: 500px;">
        <!-- Header -->
        <div style="text-align: center; margin-bottom: 2rem;">
            <div style="margin-bottom: 1.5rem;">
                <i class="bi bi-person-plus" style="font-size: 3rem; color: var(--color-primary-600);"></i>
            </div>
            <h1 style="font-size: 2rem; font-weight: 700; color: var(--color-gray-900); margin-bottom: 0.5rem;">Create your account</h1>
            <p style="color: var(--color-gray-600);">Join ZATCA E-Invoice Management System</p>
        </div>

        <!-- Registration Form Card -->
        <div class="modern-card" style="padding: 2rem;">
            <form id="registerForm" asp-route-returnUrl="@Model.ReturnUrl" method="post">
                <div asp-validation-summary="ModelOnly" class="modern-alert modern-alert-error" role="alert"></div>

                <!-- Name Fields Row -->
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1.5rem;">
                    <!-- First Name -->
                    <div class="modern-form-group">
                        <div class="modern-floating-input-container">
                            <input asp-for="Input.FirstName"
                                   class="modern-floating-input"
                                   type="text"
                                   autocomplete="given-name"
                                   aria-required="true"
                                   placeholder=" " />
                            <label asp-for="Input.FirstName" class="modern-floating-label">
                                <i class="bi bi-person"></i> First Name
                            </label>
                        </div>
                        <span asp-validation-for="Input.FirstName" class="modern-form-error"></span>
                    </div>

                    <!-- Last Name -->
                    <div class="modern-form-group">
                        <div class="modern-floating-input-container">
                            <input asp-for="Input.LastName"
                                   class="modern-floating-input"
                                   type="text"
                                   autocomplete="family-name"
                                   aria-required="true"
                                   placeholder=" " />
                            <label asp-for="Input.LastName" class="modern-floating-label">
                                <i class="bi bi-person"></i> Last Name
                            </label>
                        </div>
                        <span asp-validation-for="Input.LastName" class="modern-form-error"></span>
                    </div>
                </div>

                <!-- Company Name -->
                <div class="modern-form-group">
                    <div class="modern-floating-input-container">
                        <input asp-for="Input.CompanyName"
                               class="modern-floating-input"
                               type="text"
                               autocomplete="organization"
                               placeholder=" " />
                        <label asp-for="Input.CompanyName" class="modern-floating-label">
                            <i class="bi bi-building"></i> Company Name (Optional)
                        </label>
                    </div>
                    <span asp-validation-for="Input.CompanyName" class="modern-form-error"></span>
                </div>

                <!-- Email Field -->
                <div class="modern-form-group">
                    <div class="modern-floating-input-container">
                        <input asp-for="Input.Email"
                               class="modern-floating-input"
                               type="email"
                               autocomplete="email"
                               aria-required="true"
                               placeholder=" " />
                        <label asp-for="Input.Email" class="modern-floating-label">
                            <i class="bi bi-envelope"></i> Email Address
                        </label>
                    </div>
                    <span asp-validation-for="Input.Email" class="modern-form-error"></span>
                </div>

                <!-- Password Field -->
                <div class="modern-form-group">
                    <div class="modern-floating-input-container">
                        <input asp-for="Input.Password"
                               class="modern-floating-input"
                               type="password"
                               autocomplete="new-password"
                               aria-required="true"
                               placeholder=" " />
                        <label asp-for="Input.Password" class="modern-floating-label">
                            <i class="bi bi-lock"></i> Password
                        </label>
                    </div>
                    <span asp-validation-for="Input.Password" class="modern-form-error"></span>
                    <div style="font-size: 0.75rem; color: var(--color-gray-500); margin-top: 0.25rem;">
                        Password must be at least 6 characters long and contain uppercase, lowercase, and numbers.
                    </div>
                </div>

                <!-- Confirm Password Field -->
                <div class="modern-form-group">
                    <div class="modern-floating-input-container">
                        <input asp-for="Input.ConfirmPassword"
                               class="modern-floating-input"
                               type="password"
                               autocomplete="new-password"
                               aria-required="true"
                               placeholder=" " />
                        <label asp-for="Input.ConfirmPassword" class="modern-floating-label">
                            <i class="bi bi-shield-check"></i> Confirm Password
                        </label>
                    </div>
                    <span asp-validation-for="Input.ConfirmPassword" class="modern-form-error"></span>
                </div>

                <!-- Submit Button -->
                <button id="registerSubmit" type="submit" class="modern-btn modern-btn-primary" style="width: 100%; margin-bottom: 1.5rem;">
                    <i class="bi bi-person-plus"></i>
                    Create Account
                </button>

                <!-- Links -->
                <div style="text-align: center;">
                    <p style="font-size: 0.875rem; color: var(--color-gray-600);">
                        Already have an account?
                        <a asp-page="./Login" asp-route-returnUrl="@Model.ReturnUrl" style="color: var(--color-primary-600); text-decoration: none; font-weight: 500;">
                            Sign in here
                        </a>
                    </p>
                </div>
            </form>
        </div>

        @if ((Model.ExternalLogins?.Count ?? 0) > 0)
        {
            <!-- External Login Section -->
            <div class="modern-card" style="margin-top: 1.5rem; padding: 2rem;">
                <div style="text-align: center; margin-bottom: 1rem;">
                    <h3 style="font-size: 1.125rem; font-weight: 500; color: var(--color-gray-900);">Or register with</h3>
                </div>
                <form id="external-account" asp-page="./ExternalLogin" asp-route-returnUrl="@Model.ReturnUrl" method="post">
                    @foreach (var provider in Model.ExternalLogins!)
                    {
                        <button type="submit"
                                class="modern-btn modern-btn-secondary"
                                style="width: 100%; margin-bottom: 0.75rem;"
                                name="provider"
                                value="@provider.Name"
                                title="Register using your @provider.DisplayName account">
                            @provider.DisplayName
                        </button>
                    }
                </form>
            </div>
        }

        <!-- Footer -->
        <div style="text-align: center; margin-top: 1.5rem;">
            <p style="font-size: 0.75rem; color: var(--color-gray-500);">
                By creating an account, you agree to our terms of service and privacy policy.
            </p>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
