using Application.Contracts.IServices;
using Application.Models.Zatca;
using Domain.Entities;
using Microsoft.Extensions.Logging;

namespace Presentation.Services
{
    /// <summary>
    /// Mock implementation of IZatcaInvoiceSender for testing environment functionality
    /// </summary>
    public class MockZatcaInvoiceSender : IZatcaInvoiceSender
    {
        private readonly ILogger<MockZatcaInvoiceSender> _logger;

        public MockZatcaInvoiceSender(ILogger<MockZatcaInvoiceSender> logger)
        {
            _logger = logger;
        }

        public async Task<ZatcaInvoiceResultResponse> SendInvoiceToZatcaAsync(InvoiceToZatca invoice)
        {
            _logger.LogInformation("Mock: Sending invoice to ZATCA environment: {Environment}", SharedData.APIUrl);

            // Simulate processing delay
            await Task.Delay(100);

            return new ZatcaInvoiceResultResponse
            {
                StatusCode = 200,
                Message = "Mock invoice submission successful",
                ReportedToZatca = true,
                InvoiceHash = "mock-hash-123",
                WarningMessages = new List<ValidationResultMessage>(),
                ErrorMessages = new List<ValidationResultMessage>()
            };
        }

        public async Task<ZatcaInvoiceResultResponse> SendInvoiceToZatcaAsync()
        {
            _logger.LogInformation("Mock: Sending invoice to ZATCA environment (no parameters): {Environment}", SharedData.APIUrl);

            // Simulate processing delay
            await Task.Delay(100);

            return new ZatcaInvoiceResultResponse
            {
                StatusCode = 200,
                Message = "Mock invoice submission successful (no parameters)",
                ReportedToZatca = true,
                InvoiceHash = "mock-hash-456",
                WarningMessages = new List<ValidationResultMessage>(),
                ErrorMessages = new List<ValidationResultMessage>()
            };
        }

        public async Task<ZatcaInvoiceResultResponse> SendInvoiceToZatcaAsync(InvoiceToZatca invoice, Seller supplier, CertificateSettings certificateDetails)
        {
            _logger.LogInformation("Mock: Sending invoice to ZATCA environment with supplier and certificate: {Environment}", SharedData.APIUrl);

            // Simulate processing delay
            await Task.Delay(100);

            return new ZatcaInvoiceResultResponse
            {
                StatusCode = 200,
                Message = "Mock invoice submission successful (with supplier and certificate)",
                ReportedToZatca = true,
                InvoiceHash = "mock-hash-789",
                WarningMessages = new List<ValidationResultMessage>(),
                ErrorMessages = new List<ValidationResultMessage>()
            };
        }

        public async Task<ZatcaInvoiceResultResponse> SendInvoiceToZatcaAsync(InvoiceToZatca invoice, CertificateSettings certificateDetails)
        {
            _logger.LogInformation("Mock: Sending invoice to ZATCA environment with certificate: {Environment}", SharedData.APIUrl);

            // Simulate processing delay
            await Task.Delay(100);

            return new ZatcaInvoiceResultResponse
            {
                StatusCode = 200,
                Message = "Mock invoice submission successful (with certificate)",
                ReportedToZatca = true,
                InvoiceHash = "mock-hash-101112",
                WarningMessages = new List<ValidationResultMessage>(),
                ErrorMessages = new List<ValidationResultMessage>()
            };
        }
    }
}
