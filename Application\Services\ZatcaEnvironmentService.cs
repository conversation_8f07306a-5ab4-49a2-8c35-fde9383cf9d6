using Application.Contracts.IServices;
using Application.Models.Configuration;
using Application.Models.Zatca;
using Domain.Enums;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Application.Services
{
    /// <summary>
    /// Service for managing ZATCA environment selection and URL resolution
    /// </summary>
    public class ZatcaEnvironmentService : IZatcaEnvironmentService
    {
        private readonly ZatcaSettings _zatcaSettings;
        private readonly ILogger<ZatcaEnvironmentService> _logger;

        public ZatcaEnvironmentService(
            IOptions<ZatcaSettings> zatcaSettings,
            ILogger<ZatcaEnvironmentService> logger)
        {
            _zatcaSettings = zatcaSettings.Value;
            _logger = logger;
        }

        /// <summary>
        /// Gets the current ZATCA environment
        /// </summary>
        /// <returns>The current environment</returns>
        public ZatcaEnvironment GetCurrentEnvironment()
        {
            return _zatcaSettings.Environment;
        }

        /// <summary>
        /// Sets the ZATCA environment for subsequent API calls
        /// </summary>
        /// <param name="environment">The environment to set</param>
        /// <returns>The URL for the specified environment</returns>
        public string SetEnvironment(ZatcaEnvironment environment)
        {
            _logger.LogInformation("Switching ZATCA environment from {CurrentEnvironment} to {NewEnvironment}",
                _zatcaSettings.Environment, environment);

            var url = _zatcaSettings.SetEnvironment(environment);
            
            // Update the SharedData.APIUrl for backward compatibility
            SharedData.APIUrl = url;

            _logger.LogInformation("ZATCA environment set to {Environment} with URL: {Url}",
                environment, url);

            return url;
        }

        /// <summary>
        /// Gets the URL for the current environment
        /// </summary>
        /// <returns>The URL for the current environment</returns>
        public string GetCurrentEnvironmentUrl()
        {
            var url = _zatcaSettings.GetCurrentEnvironmentUrl();
            
            // Ensure SharedData.APIUrl is synchronized
            if (SharedData.APIUrl != url)
            {
                SharedData.APIUrl = url;
            }

            return url;
        }

        /// <summary>
        /// Gets the URL for a specific environment
        /// </summary>
        /// <param name="environment">The environment to get the URL for</param>
        /// <returns>The URL for the specified environment</returns>
        public string GetEnvironmentUrl(ZatcaEnvironment environment)
        {
            return environment switch
            {
                ZatcaEnvironment.Developer => _zatcaSettings.DeveloperUrl,
                ZatcaEnvironment.Simulation => _zatcaSettings.SimulationUrl,
                ZatcaEnvironment.Production => _zatcaSettings.ProductionUrl,
                _ => _zatcaSettings.SimulationUrl // Default to simulation
            };
        }

        /// <summary>
        /// Gets the name of the current environment
        /// </summary>
        /// <returns>The environment name as a string</returns>
        public string GetCurrentEnvironmentName()
        {
            return _zatcaSettings.GetEnvironmentName();
        }

        /// <summary>
        /// Validates that the ZATCA settings are properly configured
        /// </summary>
        /// <returns>True if settings are valid, false otherwise</returns>
        public bool ValidateSettings()
        {
            var isValid = _zatcaSettings.IsValid();
            
            if (!isValid)
            {
                _logger.LogError("ZATCA settings validation failed. Please check the configuration.");
            }

            return isValid;
        }
    }
}
