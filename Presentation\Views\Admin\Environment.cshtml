@model EnvironmentManagementViewModel
@{
    ViewData["Title"] = "ZATCA Environment Management";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-cogs me-2"></i>ZATCA Environment Management
                </h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item"><a href="/">Home</a></li>
                        <li class="breadcrumb-item"><a href="@Url.Action("Index", "Admin")">Admin</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Environment</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Alert Messages -->
    <div id="alertContainer"></div>

    @if (TempData["Success"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>@TempData["Success"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    @if (TempData["Error"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>@TempData["Error"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <!-- Current Environment Status -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3 bg-primary text-white">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-info-circle me-2"></i>Current Environment Status
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <span class="badge bg-success fs-6 px-3 py-2" id="currentEnvironmentBadge">
                                        <i class="fas fa-circle me-1"></i>
                                        <span id="currentEnvironmentName">@Model.CurrentEnvironment</span>
                                    </span>
                                </div>
                                <div>
                                    <h5 class="mb-1 text-gray-800">Active Environment</h5>
                                    <p class="text-muted mb-0">Currently processing requests</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="text-md-end">
                                <h6 class="text-gray-800 mb-1">Current URL:</h6>
                                <code id="currentEnvironmentUrl" class="bg-light p-2 rounded">@Model.CurrentUrl</code>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Environment Selection -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-exchange-alt me-2"></i>Switch Environment
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        @foreach (var env in Model.AvailableEnvironments)
                        {
                            <div class="col-lg-4 col-md-6 mb-3">
                                <div class="card environment-card @(env.IsActive ? "border-success" : "border-light")" 
                                     data-environment="@((int)env.Environment)" 
                                     data-environment-name="@env.Name"
                                     data-environment-url="@env.Url">
                                    <div class="card-body text-center">
                                        <div class="mb-3">
                                            @if (env.Environment == Domain.Enums.ZatcaEnvironment.Developer)
                                            {
                                                <i class="fas fa-code fa-3x text-info"></i>
                                            }
                                            else if (env.Environment == Domain.Enums.ZatcaEnvironment.Simulation)
                                            {
                                                <i class="fas fa-flask fa-3x text-warning"></i>
                                            }
                                            else
                                            {
                                                <i class="fas fa-rocket fa-3x text-success"></i>
                                            }
                                        </div>
                                        <h5 class="card-title">@env.Name</h5>
                                        <p class="card-text text-muted small">
                                            <code>@env.Url</code>
                                        </p>
                                        @if (env.IsActive)
                                        {
                                            <button class="btn btn-success btn-sm" disabled>
                                                <i class="fas fa-check me-1"></i>Active
                                            </button>
                                        }
                                        else
                                        {
                                            <button class="btn btn-primary btn-sm switch-environment-btn" 
                                                    data-environment="@((int)env.Environment)">
                                                <i class="fas fa-arrow-right me-1"></i>Switch
                                            </button>
                                        }
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Environment Information -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle me-2"></i>Environment Information
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <h6 class="font-weight-bold text-info">
                                <i class="fas fa-code me-1"></i>Developer
                            </h6>
                            <p class="text-muted small">
                                Use for development and initial testing. This environment provides 
                                developer-specific features and debugging capabilities.
                            </p>
                        </div>
                        <div class="col-md-4">
                            <h6 class="font-weight-bold text-warning">
                                <i class="fas fa-flask me-1"></i>Simulation
                            </h6>
                            <p class="text-muted small">
                                Use for testing with simulated ZATCA services. This environment 
                                mimics production behavior without affecting real data.
                            </p>
                        </div>
                        <div class="col-md-4">
                            <h6 class="font-weight-bold text-success">
                                <i class="fas fa-rocket me-1"></i>Production
                            </h6>
                            <p class="text-muted small">
                                Use for live operations with real ZATCA services. All transactions 
                                in this environment are processed as actual submissions.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center py-4">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mb-0">Switching environment...</p>
            </div>
        </div>
    </div>
</div>

<style>
.environment-card {
    transition: all 0.3s ease;
    cursor: pointer;
}

.environment-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.environment-card.border-success {
    border-width: 2px !important;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

.bg-light {
    background-color: #f8f9fc !important;
}
</style>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Environment switching functionality
            $('.switch-environment-btn').on('click', function() {
                const environmentValue = $(this).data('environment');
                const environmentName = $(this).closest('.environment-card').data('environment-name');

                switchEnvironment(environmentValue, environmentName);
            });

            // Auto-refresh environment status every 30 seconds
            setInterval(refreshEnvironmentStatus, 30000);
        });

        function switchEnvironment(environmentValue, environmentName) {
            // Show loading modal
            $('#loadingModal').modal('show');

            // Disable all switch buttons
            $('.switch-environment-btn').prop('disabled', true);

            const requestData = {
                environment: environmentValue
            };

            $.ajax({
                url: '/api/v1.0/Invoicing/environment',
                type: 'PUT',
                contentType: 'application/json',
                data: JSON.stringify(requestData),
                success: function(response) {
                    if (response.success) {
                        showAlert('success', `Environment successfully switched to ${environmentName}!`);
                        updateEnvironmentUI(response.data);
                    } else {
                        showAlert('danger', 'Failed to switch environment: ' + (response.message || 'Unknown error'));
                    }
                },
                error: function(xhr, status, error) {
                    let errorMessage = 'Failed to switch environment';

                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage += ': ' + xhr.responseJSON.message;
                    } else if (xhr.responseText) {
                        try {
                            const errorResponse = JSON.parse(xhr.responseText);
                            errorMessage += ': ' + (errorResponse.message || errorResponse.title || error);
                        } catch (e) {
                            errorMessage += ': ' + error;
                        }
                    }

                    showAlert('danger', errorMessage);
                },
                complete: function() {
                    // Hide loading modal
                    $('#loadingModal').modal('hide');

                    // Re-enable switch buttons
                    $('.switch-environment-btn').prop('disabled', false);
                }
            });
        }

        function updateEnvironmentUI(environmentData) {
            // Update current environment display
            $('#currentEnvironmentName').text(environmentData.currentEnvironment);
            $('#currentEnvironmentUrl').text(environmentData.currentUrl);

            // Update environment cards
            $('.environment-card').each(function() {
                const cardEnvironment = $(this).data('environment');
                const isActive = cardEnvironment === environmentData.currentEnvironment;

                // Update card styling
                if (isActive) {
                    $(this).removeClass('border-light').addClass('border-success');
                    $(this).find('.switch-environment-btn').replaceWith(
                        '<button class="btn btn-success btn-sm" disabled><i class="fas fa-check me-1"></i>Active</button>'
                    );
                } else {
                    $(this).removeClass('border-success').addClass('border-light');
                    const envValue = $(this).data('environment');
                    $(this).find('button').replaceWith(
                        `<button class="btn btn-primary btn-sm switch-environment-btn" data-environment="${envValue}">
                            <i class="fas fa-arrow-right me-1"></i>Switch
                        </button>`
                    );
                }
            });

            // Re-bind click events for new switch buttons
            $('.switch-environment-btn').off('click').on('click', function() {
                const environmentValue = $(this).data('environment');
                const environmentName = $(this).closest('.environment-card').data('environment-name');
                switchEnvironment(environmentValue, environmentName);
            });
        }

        function refreshEnvironmentStatus() {
            $.ajax({
                url: '/api/v1.0/Invoicing/environment',
                type: 'GET',
                success: function(response) {
                    if (response.success && response.data) {
                        updateEnvironmentUI(response.data);
                    }
                },
                error: function() {
                    // Silently fail for auto-refresh
                    console.log('Failed to refresh environment status');
                }
            });
        }

        function showAlert(type, message) {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            `;

            $('#alertContainer').html(alertHtml);

            // Auto-dismiss after 5 seconds
            setTimeout(function() {
                $('#alertContainer .alert').alert('close');
            }, 5000);
        }
    </script>
}
