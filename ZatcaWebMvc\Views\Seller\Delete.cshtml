@model ZatcaWebMvc.Models.SellerResponse
@{
    ViewData["Title"] = "Delete Seller";
}

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>Delete Seller
                    </h3>
                </div>
                <div class="card-body">
                    @if (!string.IsNullOrEmpty(ViewBag.ErrorMessage))
                    {
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>@ViewBag.ErrorMessage
                        </div>
                    }

                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Warning:</strong> Are you sure you want to delete this seller? This action cannot be undone.
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Organization Name</label>
                            <p class="form-control-plaintext">@Model.SellerName</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Tax Registration Number (TRN)</label>
                            <p class="form-control-plaintext">
                                <span class="badge bg-info fs-6">@Model.SellerTRN</span>
                            </p>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Email Address</label>
                            <p class="form-control-plaintext">@Model.Email</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Phone Number</label>
                            <p class="form-control-plaintext">@Model.Phone</p>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">City</label>
                            <p class="form-control-plaintext">@Model.CityName</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Business Category</label>
                            <p class="form-control-plaintext">@Model.BusinessCategory</p>
                        </div>
                    </div>

                    <form asp-action="Delete" asp-route-id="@Model.SellerId" method="post" class="d-inline">
                        <div class="d-flex justify-content-between">
                            <a asp-action="Index" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-danger" 
                                    onclick="return confirm('Are you absolutely sure you want to delete this seller?')">
                                <i class="fas fa-trash me-1"></i>Delete Seller
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
