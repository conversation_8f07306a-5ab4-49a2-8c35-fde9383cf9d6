using Domain.Enums;

namespace Presentation.Models
{
    /// <summary>
    /// View model for the environment management page
    /// </summary>
    public class EnvironmentManagementViewModel
    {
        /// <summary>
        /// The currently active ZATCA environment
        /// </summary>
        public ZatcaEnvironment CurrentEnvironment { get; set; }

        /// <summary>
        /// The name of the current environment
        /// </summary>
        public string CurrentEnvironmentName { get; set; } = string.Empty;

        /// <summary>
        /// The URL for the current environment
        /// </summary>
        public string CurrentUrl { get; set; } = string.Empty;

        /// <summary>
        /// List of available environments with their details
        /// </summary>
        public List<EnvironmentOptionViewModel> AvailableEnvironments { get; set; } = new();
    }

    /// <summary>
    /// Represents an environment option for the admin interface
    /// </summary>
    public class EnvironmentOptionViewModel
    {
        /// <summary>
        /// The environment enum value
        /// </summary>
        public ZatcaEnvironment Environment { get; set; }

        /// <summary>
        /// The display name of the environment
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// The URL for this environment
        /// </summary>
        public string Url { get; set; } = string.Empty;

        /// <summary>
        /// Whether this environment is currently active
        /// </summary>
        public bool IsActive { get; set; }
    }
}
