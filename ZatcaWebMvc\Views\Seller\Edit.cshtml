@model ZatcaWebMvc.Models.SellerCreateRequest
@{
    ViewData["Title"] = "Edit Seller";
    var sellerId = ViewBag.SellerId;
}

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-edit me-2"></i>Edit Seller
                    </h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Note:</strong> Editing seller information is not supported by the ZATCA API.
                        If you need to make changes, please delete the current seller and create a new one.
                    </div>

                    @if (!string.IsNullOrEmpty(ViewBag.ErrorMessage))
                    {
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>@ViewBag.ErrorMessage
                        </div>
                    }

                    <form asp-action="Edit" asp-route-id="@sellerId" method="post">
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger"></div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="SellerName" class="form-label">Organization Name</label>
                                <input asp-for="SellerName" class="form-control" placeholder="Enter organization name" />
                                <span asp-validation-for="SellerName" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="SellerTRN" class="form-label">Tax Registration Number (TRN)</label>
                                <input asp-for="SellerTRN" class="form-control" placeholder="3XXXXXXXXXXXXXXXXX3" />
                                <div class="form-text">15-digit number starting and ending with 3</div>
                                <span asp-validation-for="SellerTRN" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Email" class="form-label">Email Address</label>
                                <input asp-for="Email" type="email" class="form-control" placeholder="Enter email address" />
                                <span asp-validation-for="Email" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="Phone" class="form-label">Phone Number</label>
                                <input asp-for="Phone" class="form-control" placeholder="Enter phone number" />
                                <span asp-validation-for="Phone" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="StreetName" class="form-label">Street Name</label>
                                <input asp-for="StreetName" class="form-control" placeholder="Enter street name" />
                                <span asp-validation-for="StreetName" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="BuildingNumber" class="form-label">Building Number</label>
                                <input asp-for="BuildingNumber" class="form-control" placeholder="Enter building number" />
                                <div class="form-text">4-digit building number</div>
                                <span asp-validation-for="BuildingNumber" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="CityName" class="form-label">City</label>
                                <input asp-for="CityName" class="form-control" placeholder="Enter city name" />
                                <span asp-validation-for="CityName" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="DistrictName" class="form-label">District</label>
                                <input asp-for="DistrictName" class="form-control" placeholder="Enter district name" />
                                <span asp-validation-for="DistrictName" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="PostalCode" class="form-label">Postal Code</label>
                                <input asp-for="PostalCode" class="form-control" placeholder="Enter postal code" />
                                <div class="form-text">5-digit postal code</div>
                                <span asp-validation-for="PostalCode" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="AdditionalStreetAddress" class="form-label">Additional Street Address</label>
                                <input asp-for="AdditionalStreetAddress" class="form-control" placeholder="Enter additional address" />
                                <span asp-validation-for="AdditionalStreetAddress" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="IdentityType" class="form-label">Identity Type</label>
                                <select asp-for="IdentityType" class="form-select">
                                    <option value="CRN">Commercial Registration Number (CRN)</option>
                                    <option value="TIN">Tax Identification Number (TIN)</option>
                                    <option value="NAT">National ID</option>
                                </select>
                                <span asp-validation-for="IdentityType" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="IdentityNumber" class="form-label">Identity Number</label>
                                <input asp-for="IdentityNumber" class="form-control" placeholder="Enter identity number" />
                                <span asp-validation-for="IdentityNumber" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="CountryCode" class="form-label">Country Code</label>
                                <input asp-for="CountryCode" class="form-control" readonly />
                                <div class="form-text">Saudi Arabia (SA)</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="BusinessCategory" class="form-label">Business Category</label>
                                <input asp-for="BusinessCategory" class="form-control" placeholder="Enter business category" />
                                <span asp-validation-for="BusinessCategory" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a asp-action="Index" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>Back to List
                            </a>
                            <button type="button" class="btn btn-warning" disabled>
                                <i class="fas fa-save me-1"></i>Update Not Supported
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
