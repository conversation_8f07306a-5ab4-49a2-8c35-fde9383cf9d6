{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  "ConnectionStrings": {
    //"ZatcaDemoDB": "Server=db6873.databaseasp.net; Database=db6873; User Id=db6873; Password=********; Encrypt=False; MultipleActiveResultSets=True;" //"Server=.;Database=zak_erp;Trusted_Connection=True;",
    "ZatcaDemoDB": "Server=.;Database=Zatca;Trusted_Connection=True; MultipleActiveResultSets=True;"
    //"A7MDDemoDB": "Server=.;Database=a7md_eg_QC;Trusted_Connection=True;"
    //"A7MDDemoDB": "Server=.;Database=Ka3kaDemo;Trusted_Connection=True;",
  },
  "ZatcaSettings": {
    "Environment": "Simulation",
    "DeveloperUrl": "https://gw-fatoora.zatca.gov.sa/e-invoicing/developer-portal/", //"https://gw-apic-gov.gazt.gov.sa/e-invoicing/developer-portal/"
    "ProductionUrl": "https://gw-fatoora.zatca.gov.sa/e-invoicing/core/",
    "SimulationUrl": "https://gw-fatoora.zatca.gov.sa/e-invoicing/simulation/",
   // "Url": "https://gw-fatoora.zatca.gov.sa/e-invoicing/simulation/",
    "Otp": "063116"
  },
  "Paths": {
    "XMLPath": "C:\\A7MD Invoice\\"
  }
}
