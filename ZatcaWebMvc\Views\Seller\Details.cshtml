@model ZatcaWebMvc.Models.SellerResponse
@{
    ViewData["Title"] = "Seller Details";
}

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-building me-2"></i>Seller Details
                    </h3>
                </div>
                <div class="card-body">
                    @if (!string.IsNullOrEmpty(ViewBag.ErrorMessage))
                    {
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>@ViewBag.ErrorMessage
                        </div>
                    }

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Organization Name</label>
                            <p class="form-control-plaintext">@Model.SellerName</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Tax Registration Number (TRN)</label>
                            <p class="form-control-plaintext">
                                <span class="badge bg-info fs-6">@Model.SellerTRN</span>
                            </p>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Email Address</label>
                            <p class="form-control-plaintext">
                                <a href="mailto:@Model.Email">@Model.Email</a>
                            </p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Phone Number</label>
                            <p class="form-control-plaintext">@Model.Phone</p>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Street Name</label>
                            <p class="form-control-plaintext">@Model.StreetName</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Building Number</label>
                            <p class="form-control-plaintext">@Model.BuildingNumber</p>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">City</label>
                            <p class="form-control-plaintext">@Model.CityName</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">District</label>
                            <p class="form-control-plaintext">@Model.DistrictName</p>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Postal Code</label>
                            <p class="form-control-plaintext">@Model.PostalCode</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Additional Street Address</label>
                            <p class="form-control-plaintext">@Model.AdditionalStreetAddress</p>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Identity Type</label>
                            <p class="form-control-plaintext">
                                @switch (Model.IdentityType)
                                {
                                    case "CRN":
                                        <span class="badge bg-primary">Commercial Registration Number</span>
                                        break;
                                    case "TIN":
                                        <span class="badge bg-secondary">Tax Identification Number</span>
                                        break;
                                    case "NAT":
                                        <span class="badge bg-success">National ID</span>
                                        break;
                                    default:
                                        <span class="badge bg-light text-dark">@Model.IdentityType</span>
                                        break;
                                }
                            </p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Identity Number</label>
                            <p class="form-control-plaintext">@Model.IdentityNumber</p>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Country Code</label>
                            <p class="form-control-plaintext">
                                <span class="badge bg-success">@Model.CountryCode</span>
                            </p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Business Category</label>
                            <p class="form-control-plaintext">@Model.BusinessCategory</p>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Back to List
                        </a>
                        <div>
                            <a asp-action="Edit" asp-route-id="@Model.SellerId" class="btn btn-warning">
                                <i class="fas fa-edit me-1"></i>Edit
                            </a>
                            <a asp-action="Delete" asp-route-id="@Model.SellerId" class="btn btn-danger">
                                <i class="fas fa-trash me-1"></i>Delete
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
