﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.Enums
{
    public enum InvoiceType
    {
        Standard = 0100000,
        Simplified = 0200000
    }

    public enum InvoiceTypeCode
    {
        Invoice = 388,
        Debit = 383,
        Credit = 381,
        Prepayment = 386
    }

    /// <summary>
    /// Represents the different ZATCA environments available for invoice processing
    /// </summary>
    public enum ZatcaEnvironment
    {
        /// <summary>
        /// Developer environment for initial development and testing
        /// </summary>
        Developer = 1,

        /// <summary>
        /// Simulation environment for testing with simulated data
        /// </summary>
        Simulation = 2,

        /// <summary>
        /// Production environment for live invoice processing
        /// </summary>
        Production = 3
    }
}
