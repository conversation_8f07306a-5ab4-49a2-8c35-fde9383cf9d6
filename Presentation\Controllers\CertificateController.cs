﻿using Application.Contracts.IServices;
using Application.Contracts.Zatca;
using Application.Dtos.Requests;
using Application.Models.Zatca;
using Domain.Entities;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using NuGet.Protocol;
using Presentation.Models;
using System.ComponentModel.DataAnnotations;
using Application.Services;

namespace Presentation.Controllers
{
    /// <summary>
    /// Controller for managing ZATCA certificates and CSR operations
    /// </summary>
    [Route("api/v{version:apiVersion}/[controller]")]
    [ApiController]
    [ApiVersion("1.0")]
    [Produces("application/json")]
    [Tags("Certificate Management")]
    public class CertificateController : ControllerBase
    {
        private readonly ICertificateCreationService _certificateCreationService;
        private readonly ICertificateConfiguration _certificateConfiguration;
        private readonly IConfiguration _config;
        private readonly ILogger<CertificateController> _logger;
        private readonly IZatcaEnvironmentService _zatcaEnvironmentService;

        /// <summary>
        /// Initializes a new instance of the CertificateController
        /// </summary>
        /// <param name="certificateConfiguration">Service for certificate configuration</param>
        /// <param name="certificateCreation">Service for certificate creation</param>
        /// <param name="config">Application configuration</param>
        /// <param name="logger">Logger instance</param>
        /// <param name="zatcaEnvironmentService">Service for ZATCA environment management</param>
        public CertificateController(
            ICertificateConfiguration certificateConfiguration,
            ICertificateCreationService certificateCreation,
            IConfiguration config,
            ILogger<CertificateController> logger,
            IZatcaEnvironmentService zatcaEnvironmentService)
        {
            _certificateConfiguration = certificateConfiguration;
            _certificateCreationService = certificateCreation;
            _config = config;
            _logger = logger;
            _zatcaEnvironmentService = zatcaEnvironmentService;
        }
        /// <summary>
        /// Retrieves all available certificates
        /// </summary>
        /// <returns>List of certificates with their details</returns>
        /// <response code="200">Successfully retrieved certificates</response>
        /// <response code="404">No certificates found</response>
        /// <response code="500">Internal server error</response>
        [HttpGet]
        [ProducesResponseType(typeof(ApiResponse<List<CertificateSettings>>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 404)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<IActionResult> GetCertificates()
        {
            try
            {
                _logger.LogInformation("Retrieving all certificates");

                var certificates = await _certificateConfiguration.GetCertificatesDetails();
                var certificateList = certificates.ToList();

                if (certificateList.Any())
                {
                    _logger.LogInformation("Found {Count} certificates", certificateList.Count);
                    return Ok(ApiResponse<List<CertificateSettings>>.SuccessResponse(
                        certificateList,
                        "Certificates retrieved successfully"));
                }
                else
                {
                    _logger.LogInformation("No certificates found");
                    return NotFound(ApiResponse.ErrorResponse(
                        "No certificates found",
                        404));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving certificates");
                return StatusCode(500, ApiResponse.ErrorResponse(
                    "An error occurred while retrieving certificates",
                    500,
                    new List<string> { ex.Message }));
            }
        }

        /// <summary>
        /// Retrieves certificate details by ID
        /// </summary>
        /// <param name="certificateId">The certificate ID to retrieve</param>
        /// <returns>Certificate details</returns>
        /// <response code="200">Certificate found and returned</response>
        /// <response code="404">Certificate not found</response>
        /// <response code="500">Internal server error</response>
        [HttpGet("details/{certificateId}")]
        [ProducesResponseType(typeof(ApiResponse<CertificateSettings>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 404)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<IActionResult> GetCertificateDetails([Required] string certificateId)
        {
            if (string.IsNullOrWhiteSpace(certificateId))
            {
                return BadRequest(ApiResponse.ErrorResponse(
                    "Certificate ID is required",
                    400));
            }

            try
            {
                _logger.LogInformation("Retrieving certificate details for ID: {CertificateId}", certificateId);

                var certificateSettings = await _certificateConfiguration.GetCertificateDetails(certificateId);

                if (certificateSettings != null)
                {
                    _logger.LogInformation("Certificate found for ID: {CertificateId}", certificateId);
                    return Ok(ApiResponse<CertificateSettings>.SuccessResponse(
                        certificateSettings,
                        "Certificate details retrieved successfully"));
                }
                else
                {
                    _logger.LogWarning("Certificate not found for ID: {CertificateId}", certificateId);
                    return NotFound(ApiResponse.ErrorResponse(
                        $"Certificate with ID '{certificateId}' not found",
                        404));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving certificate details for ID: {CertificateId}", certificateId);
                return StatusCode(500, ApiResponse.ErrorResponse(
                    "An error occurred while retrieving certificate details",
                    500,
                    new List<string> { ex.Message }));
            }
        }

        /// <summary>
        /// Creates a new certificate for ZATCA compliance using the currently selected environment
        /// </summary>
        /// <remarks>
        /// This endpoint dynamically uses the appropriate ZATCA URL based on the currently selected environment:
        /// - Developer: Uses ZATCA developer/sandbox URL
        /// - Simulation: Uses ZATCA simulation URL
        /// - Production: Uses ZATCA production URL
        ///
        /// Environment can be changed through the admin interface at runtime.
        /// </remarks>
        /// <param name="zatcaCsr">Certificate signing request data</param>
        /// <param name="otp">One-time password from ZATCA</param>
        /// <returns>Created certificate details</returns>
        /// <response code="200">Certificate created successfully</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="422">Certificate creation failed</response>
        /// <response code="500">Internal server error</response>
        [HttpPost]
        [ProducesResponseType(typeof(ApiResponse<CertificateCreationResponse>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        [ProducesResponseType(typeof(ApiResponse), 422)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<IActionResult> CreateCertificate(
            [FromBody] ZatcaCsrCreationRequestDto zatcaCsr,
            [FromQuery, Required] string otp)
        {
            if (zatcaCsr == null)
            {
                _logger.LogWarning("Certificate creation failed: Null CSR data received");
                return BadRequest(ApiResponse.ErrorResponse(
                    "Certificate signing request data is required",
                    400));
            }

            if (string.IsNullOrWhiteSpace(otp))
            {
                return BadRequest(ApiResponse.ErrorResponse(
                    "OTP (One-Time Password) is required",
                    400));
            }

            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values
                    .SelectMany(v => v.Errors)
                    .Select(e => e.ErrorMessage)
                    .ToList();

                _logger.LogWarning("Certificate creation failed: Validation errors - {Errors}", string.Join(", ", errors));
                return BadRequest(ApiResponse.ValidationErrorResponse(errors));
            }

            try
            {
                // Get current environment and set appropriate URL
                var currentEnvironment = _zatcaEnvironmentService.GetCurrentEnvironment();
                var environmentUrl = _zatcaEnvironmentService.GetCurrentEnvironmentUrl();

                _logger.LogInformation("Creating certificate for organization: {OrganizationName} using {Environment} environment ({Url})",
                    zatcaCsr.OrganizationName, currentEnvironment, environmentUrl);

                // Set the environment URL for the certificate creation process
                SharedData.APIUrl = environmentUrl;
                _config["ZatcaSettings:Otp"] = otp;

                var result = await _certificateCreationService.CreateCertificateAsync(zatcaCsr);

                if (result.Success)
                {
                    _logger.LogInformation("Certificate created successfully for organization: {OrganizationName} in {Environment} environment",
                        zatcaCsr.OrganizationName, currentEnvironment);

                    var response = MapToCertificateCreationResponse(result.Data, zatcaCsr.IsProduction);
                    return Ok(ApiResponse<CertificateCreationResponse>.SuccessResponse(
                        response,
                        $"Certificate created successfully in {currentEnvironment} environment"));
                }
                else
                {
                    _logger.LogWarning("Certificate creation failed for organization: {OrganizationName}. Error: {Error}",
                        zatcaCsr.OrganizationName, result.Message);
                    return UnprocessableEntity(ApiResponse.ErrorResponse(
                        result.Message ?? "Certificate creation failed",
                        422));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating certificate for organization: {OrganizationName}", zatcaCsr.OrganizationName);
                return StatusCode(500, ApiResponse.ErrorResponse(
                    "An error occurred while creating the certificate",
                    500,
                    new List<string> { ex.Message }));
            }
        }

        /// <summary>
        /// Deletes a certificate by ID
        /// </summary>
        /// <param name="id">The certificate ID to delete</param>
        /// <returns>Deletion result</returns>
        /// <response code="200">Certificate deleted successfully</response>
        /// <response code="404">Certificate not found</response>
        /// <response code="500">Internal server error</response>
        [HttpDelete("{id}")]
        [ProducesResponseType(typeof(ApiResponse), 200)]
        [ProducesResponseType(typeof(ApiResponse), 404)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<IActionResult> DeleteCertificate([Required] string id)
        {
            if (string.IsNullOrWhiteSpace(id))
            {
                return BadRequest(ApiResponse.ErrorResponse(
                    "Certificate ID is required",
                    400));
            }

            try
            {
                _logger.LogInformation("Deleting certificate with ID: {CertificateId}", id);

                var certificateSettings = await _certificateConfiguration.GetCertificateDetails(id);

                if (certificateSettings != null)
                {
                    await _certificateConfiguration.RemoveCertificate(certificateSettings);

                    _logger.LogInformation("Certificate with ID {CertificateId} deleted successfully", id);
                    return Ok(ApiResponse.SuccessResponse(
                        $"Certificate with ID '{id}' was successfully deleted"));
                }
                else
                {
                    _logger.LogWarning("Certificate with ID {CertificateId} not found for deletion", id);
                    return NotFound(ApiResponse.ErrorResponse(
                        $"Certificate with ID '{id}' not found",
                        404));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting certificate with ID: {CertificateId}", id);
                return StatusCode(500, ApiResponse.ErrorResponse(
                    "An error occurred while deleting the certificate",
                    500,
                    new List<string> { ex.Message }));
            }
        }
        /// <summary>
        /// Creates a new ZATCA certificate for simulation environment
        /// </summary>
        /// <remarks>
        /// ⚠️ DEPRECATED: This endpoint is deprecated. Use the main POST /Certificate endpoint with environment switching instead.
        /// The system now uses dynamic environment selection through the admin interface.
        /// </remarks>
        /// <param name="zatcaCsr">Certificate signing request data</param>
        /// <param name="otp">One-time password from ZATCA</param>
        /// <returns>Created certificate details using current environment settings</returns>
        /// <response code="200">Certificate created successfully using current environment</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="422">Certificate creation failed</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("Simulation")]
        [ProducesResponseType(typeof(ApiResponse<CertificateCreationResponse>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        [ProducesResponseType(typeof(ApiResponse), 422)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<IActionResult> CreateCertificateSimulation(
            [FromBody] ZatcaCsrCreationRequestDto zatcaCsr,
            [FromQuery, Required] string otp)
        {
            if (zatcaCsr == null)
            {
                _logger.LogWarning("Certificate simulation failed: Null CSR data received");
                return BadRequest(ApiResponse.ErrorResponse(
                    "Certificate signing request data is required",
                    400));
            }

            if (string.IsNullOrWhiteSpace(otp))
            {
                return BadRequest(ApiResponse.ErrorResponse(
                    "OTP (One-Time Password) is required",
                    400));
            }

            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values
                    .SelectMany(v => v.Errors)
                    .Select(e => e.ErrorMessage)
                    .ToList();

                _logger.LogWarning("Certificate simulation failed: Validation errors - {Errors}", string.Join(", ", errors));
                return BadRequest(ApiResponse.ValidationErrorResponse(errors));
            }

            try
            {
                // Get current environment and set appropriate URL
                var currentEnvironment = _zatcaEnvironmentService.GetCurrentEnvironment();
                var environmentUrl = _zatcaEnvironmentService.GetCurrentEnvironmentUrl();

                _logger.LogInformation("Creating certificate for organization: {OrganizationName} using {Environment} environment ({Url}) - Note: /Simulation endpoint is deprecated, use main endpoint with environment switching",
                    zatcaCsr.OrganizationName, currentEnvironment, environmentUrl);

                // Set the environment URL for the certificate creation process
                SharedData.APIUrl = environmentUrl;
                _config["ZatcaSettings:Otp"] = otp;

                var result = await _certificateCreationService.CreateCertificateAsync(zatcaCsr);

                if (result.Success)
                {
                    _logger.LogInformation("Certificate created successfully for organization: {OrganizationName} in {Environment} environment",
                        zatcaCsr.OrganizationName, currentEnvironment);

                    var response = MapToCertificateCreationResponse(result.Data, zatcaCsr.IsProduction);
                    return Ok(ApiResponse<CertificateCreationResponse>.SuccessResponse(
                        response,
                        $"Certificate created successfully in {currentEnvironment} environment"));
                }
                else
                {
                    _logger.LogWarning("Certificate simulation failed for organization: {OrganizationName}. Error: {Error}",
                        zatcaCsr.OrganizationName, result.Message);
                    return UnprocessableEntity(ApiResponse.ErrorResponse(
                        result.Message ?? "Certificate creation failed in simulation",
                        422));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating certificate in simulation for organization: {OrganizationName}", zatcaCsr.OrganizationName);
                return StatusCode(500, ApiResponse.ErrorResponse(
                    "An error occurred while creating the certificate in simulation",
                    500,
                    new List<string> { ex.Message }));
            }
        }

        /// <summary>
        /// Maps certificate creation result to standardized response model
        /// </summary>
        private static CertificateCreationResponse MapToCertificateCreationResponse(object certificateData, bool isProduction)
        {
            // This is a simplified mapping - you may need to adjust based on the actual structure of result.Data
            return new CertificateCreationResponse
            {
                IsProduction = isProduction,
                // Map other properties based on the actual certificate data structure
                // You'll need to cast certificateData to the appropriate type and map the properties
            };
        }

    }
}
