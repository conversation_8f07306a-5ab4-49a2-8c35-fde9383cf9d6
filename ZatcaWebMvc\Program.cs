using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using ZatcaWebMvc.Data;
using ZatcaWebMvc.Models;
using ZatcaWebMvc.Services;

var builder = WebApplication.CreateBuilder(args);

// Add Entity Framework and Identity
builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection") ??
        "Server=.;Database=ZatcaWebMvcIdentity;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true;Encrypt=false"));

builder.Services.AddDefaultIdentity<ApplicationUser>(options =>
{
    // Password settings
    options.Password.RequireDigit = true;
    options.Password.RequireLowercase = true;
    options.Password.RequireNonAlphanumeric = false;
    options.Password.RequireUppercase = true;
    options.Password.RequiredLength = 6;
    options.Password.RequiredUniqueChars = 1;

    // Lockout settings
    options.Lockout.DefaultLockoutTimeSpan = TimeSpan.FromMinutes(5);
    options.Lockout.MaxFailedAccessAttempts = 5;
    options.Lockout.AllowedForNewUsers = true;

    // User settings
    options.User.AllowedUserNameCharacters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-._@+";
    options.User.RequireUniqueEmail = true;

    // Sign in settings
    options.SignIn.RequireConfirmedEmail = false;
    options.SignIn.RequireConfirmedPhoneNumber = false;
})
.AddRoles<IdentityRole>()
.AddEntityFrameworkStores<ApplicationDbContext>();

// Add services to the container.
builder.Services.AddControllersWithViews();

// Configure ZATCA API options
builder.Services.Configure<ZatcaApiOptions>(
    builder.Configuration.GetSection(ZatcaApiOptions.SectionName));

// Configure ZATCA Settings (commented out for now as Application project is not referenced)
// builder.Services.Configure<Application.Models.Configuration.ZatcaSettings>(
//     builder.Configuration.GetSection("ZatcaSettings"));

// Register ZATCA Environment Service (commented out for now as Application project is not referenced)
// builder.Services.AddScoped<Application.Contracts.IServices.IZatcaEnvironmentService, Application.Services.ZatcaEnvironmentService>();

// Add HTTP client services with enhanced configuration
builder.Services.AddHttpClient<IZatcaApiService, ZatcaApiService>()
    .ConfigureHttpClient((serviceProvider, client) =>
    {
        var config = serviceProvider.GetRequiredService<IConfiguration>();
        var baseUrl = config["ZatcaApi:BaseUrl"] ?? "http://localhost:5147";
        var timeout = config.GetValue<int>("ZatcaApi:TimeoutSeconds", 30);

        client.BaseAddress = new Uri(baseUrl);
        client.Timeout = TimeSpan.FromSeconds(timeout);
    });

// Register services
builder.Services.AddScoped<IZatcaApiService, ZatcaApiService>();
builder.Services.AddScoped<ApiConfigurationService>();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Home/Error");
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles();

app.UseRouting();

app.UseAuthentication();
app.UseAuthorization();

app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}");

app.MapRazorPages();


app.Run();
