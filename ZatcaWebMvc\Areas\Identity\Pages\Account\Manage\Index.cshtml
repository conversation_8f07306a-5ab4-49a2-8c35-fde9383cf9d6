﻿@page
@model IndexModel
@{
    ViewData["Title"] = "Profile Settings";
    ViewData["ActivePage"] = ManageNavPages.Index;
    Layout = "_Layout";
}

<div class="modern-container" style="padding: 2rem 1rem;">
    <div style="max-width: 1200px; margin: 0 auto;">
        <!-- Header -->
        <div style="margin-bottom: 2rem;">
            <h1 style="font-size: 2rem; font-weight: 700; color: var(--color-gray-900); margin-bottom: 0.5rem;">Profile Settings</h1>
            <p style="color: var(--color-gray-600);">Manage your account information and preferences</p>
        </div>

        <!-- Status Message -->
        <partial name="_StatusMessage" for="StatusMessage" />

        <div style="display: grid; grid-template-columns: 1fr 2fr; gap: 2rem;">
            <!-- Navigation Sidebar -->
            <div>
                <partial name="_ManageNav" />
            </div>

            <!-- Main Content -->
            <div>
                <div class="modern-card" style="padding: 2rem;">
                    <div style="margin-bottom: 2rem; border-bottom: 1px solid var(--color-gray-200); padding-bottom: 1rem;">
                        <h2 style="font-size: 1.25rem; font-weight: 600; color: var(--color-gray-900); margin-bottom: 0.5rem;">Personal Information</h2>
                        <p style="font-size: 0.875rem; color: var(--color-gray-600);">Update your personal details and contact information</p>
                    </div>

                    <form id="profile-form" method="post" style="display: flex; flex-direction: column; gap: 1.5rem;">
                        <div asp-validation-summary="ModelOnly" class="modern-alert modern-alert-error" role="alert" style="display: none;"></div>

                        <!-- Username (Read-only) -->
                        <div class="modern-form-group">
                            <label style="display: block; font-size: 0.875rem; font-weight: 500; color: var(--color-gray-700); margin-bottom: 0.5rem;">
                                <i class="bi bi-at" style="margin-right: 0.5rem;"></i>Username (Email)
                            </label>
                            <input asp-for="Username"
                                   style="width: 100%; padding: 0.75rem; font-size: 1rem; color: var(--color-gray-900); background: var(--color-gray-50); border: 2px solid var(--color-gray-300); border-radius: var(--radius-lg); cursor: not-allowed;"
                                   disabled
                                   readonly />
                            <div style="font-size: 0.75rem; color: var(--color-gray-500); margin-top: 0.25rem;">
                                Your username cannot be changed. Contact support if you need to update your email address.
                            </div>
                        </div>

                        <!-- Name Fields Row -->
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                            <!-- First Name -->
                            <div class="modern-form-group">
                                <div class="modern-floating-input-container">
                                    <input asp-for="Input.FirstName"
                                           class="modern-floating-input"
                                           type="text"
                                           autocomplete="given-name"
                                           aria-required="true"
                                           placeholder=" " />
                                    <label asp-for="Input.FirstName" class="modern-floating-label">
                                        <i class="bi bi-person me-2"></i>First Name
                                    </label>
                                </div>
                                <span asp-validation-for="Input.FirstName" class="modern-form-error"></span>
                            </div>

                            <!-- Last Name -->
                            <div class="modern-form-group">
                                <div class="modern-floating-input-container">
                                    <input asp-for="Input.LastName"
                                           class="modern-floating-input"
                                           type="text"
                                           autocomplete="family-name"
                                           aria-required="true"
                                           placeholder=" " />
                                    <label asp-for="Input.LastName" class="modern-floating-label">
                                        <i class="bi bi-person me-2"></i>Last Name
                                    </label>
                                </div>
                                <span asp-validation-for="Input.LastName" class="modern-form-error"></span>
                            </div>
                        </div>

                        <!-- Company Name -->
                        <div class="modern-form-group">
                            <div class="modern-floating-input-container">
                                <input asp-for="Input.CompanyName"
                                       class="modern-floating-input"
                                       type="text"
                                       autocomplete="organization"
                                       placeholder=" " />
                                <label asp-for="Input.CompanyName" class="modern-floating-label">
                                    <i class="bi bi-building me-2"></i>Company Name
                                </label>
                            </div>
                            <span asp-validation-for="Input.CompanyName" class="modern-form-error"></span>
                        </div>

                        <!-- Phone Number -->
                        <div class="modern-form-group">
                            <div class="modern-floating-input-container">
                                <input asp-for="Input.PhoneNumber"
                                       class="modern-floating-input"
                                       type="tel"
                                       autocomplete="tel"
                                       placeholder=" " />
                                <label asp-for="Input.PhoneNumber" class="modern-floating-label">
                                    <i class="bi bi-telephone me-2"></i>Phone Number
                                </label>
                            </div>
                            <span asp-validation-for="Input.PhoneNumber" class="modern-form-error"></span>
                        </div>

                        <!-- Submit Button -->
                        <div style="display: flex; justify-content: flex-end;">
                            <button id="update-profile-button" type="submit" class="modern-btn modern-btn-primary">
                                <i class="bi bi-check-circle" style="margin-right: 0.5rem;"></i>
                                Save Changes
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
