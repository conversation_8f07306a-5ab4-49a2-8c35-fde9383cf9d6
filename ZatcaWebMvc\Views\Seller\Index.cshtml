@model List<ZatcaWebMvc.Models.SellerResponse>
@{
    ViewData["Title"] = "Sellers";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-building me-2"></i>Sellers Management
                    </h3>
                    <a asp-action="Create" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>Add New Seller
                    </a>
                </div>
                <div class="card-body">
                    @if (!string.IsNullOrEmpty(ViewBag.ErrorMessage))
                    {
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>@ViewBag.ErrorMessage
                        </div>
                    }

                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show">
                            <i class="fas fa-check-circle me-2"></i>@TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    @if (Model != null && Model.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Organization Name</th>
                                        <th>TRN</th>
                                        <th>Email</th>
                                        <th>City</th>
                                        <th>Phone</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var seller in Model)
                                    {
                                        <tr>
                                            <td>
                                                <strong>@seller.SellerName</strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">@seller.SellerTRN</span>
                                            </td>
                                            <td>@seller.Email</td>
                                            <td>@seller.CityName</td>
                                            <td>@seller.Phone</td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a asp-action="Details" asp-route-id="@seller.SellerId"
                                                       class="btn btn-sm btn-outline-info" title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a asp-action="Edit" asp-route-id="@seller.SellerId"
                                                       class="btn btn-sm btn-outline-warning" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a asp-action="Delete" asp-route-id="@seller.SellerId"
                                                       class="btn btn-sm btn-outline-danger" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-building fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">No Sellers Found</h4>
                            <p class="text-muted">Get started by adding your first seller.</p>
                            <a asp-action="Create" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>Add First Seller
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>
