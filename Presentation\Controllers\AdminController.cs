using Microsoft.AspNetCore.Mvc;
using Application.Contracts.IServices;
using Domain.Enums;
using Application.Models.Configuration;
using Microsoft.Extensions.Options;
using Presentation.Models;

namespace Presentation.Controllers
{
    /// <summary>
    /// Admin controller for managing ZATCA environment settings
    /// </summary>
    public class AdminController : Controller
    {
        private readonly IZatcaEnvironmentService _zatcaEnvironmentService;
        private readonly ILogger<AdminController> _logger;
        private readonly ZatcaSettings _zatcaSettings;

        public AdminController(
            IZatcaEnvironmentService zatcaEnvironmentService,
            ILogger<AdminController> logger,
            IOptions<ZatcaSettings> zatcaSettings)
        {
            _zatcaEnvironmentService = zatcaEnvironmentService;
            _logger = logger;
            _zatcaSettings = zatcaSettings.Value;
        }

        /// <summary>
        /// Display the admin dashboard for ZATCA environment management
        /// </summary>
        /// <returns>Admin dashboard view</returns>
        public IActionResult Index()
        {
            _logger.LogInformation("Admin dashboard accessed");
            return View();
        }

        /// <summary>
        /// Display the ZATCA environment management page
        /// </summary>
        /// <returns>Environment management view</returns>
        public IActionResult Environment()
        {
            try
            {
                var currentEnvironment = _zatcaEnvironmentService.GetCurrentEnvironment();
                var currentUrl = _zatcaEnvironmentService.GetCurrentEnvironmentUrl();

                var model = new EnvironmentManagementViewModel
                {
                    CurrentEnvironment = currentEnvironment,
                    CurrentEnvironmentName = currentEnvironment.ToString(),
                    CurrentUrl = currentUrl,
                    AvailableEnvironments = Enum.GetValues<ZatcaEnvironment>()
                        .Select(env => new EnvironmentOptionViewModel
                        {
                            Environment = env,
                            Name = env.ToString(),
                            Url = _zatcaEnvironmentService.GetEnvironmentUrl(env),
                            IsActive = env == currentEnvironment
                        }).ToList()
                };

                _logger.LogInformation("Environment management page loaded. Current environment: {Environment}", 
                    currentEnvironment);

                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading environment management page");
                TempData["Error"] = "Failed to load environment information. Please try again.";
                return RedirectToAction("Index");
            }
        }
    }
}
