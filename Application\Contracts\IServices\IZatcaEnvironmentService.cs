using Domain.Enums;

namespace Application.Contracts.IServices
{
    /// <summary>
    /// Service interface for managing ZATCA environment selection and URL resolution
    /// </summary>
    public interface IZatcaEnvironmentService
    {
        /// <summary>
        /// Gets the current ZATCA environment
        /// </summary>
        /// <returns>The current environment</returns>
        ZatcaEnvironment GetCurrentEnvironment();

        /// <summary>
        /// Sets the ZATCA environment for subsequent API calls
        /// </summary>
        /// <param name="environment">The environment to set</param>
        /// <returns>The URL for the specified environment</returns>
        string SetEnvironment(ZatcaEnvironment environment);

        /// <summary>
        /// Gets the URL for the current environment
        /// </summary>
        /// <returns>The URL for the current environment</returns>
        string GetCurrentEnvironmentUrl();

        /// <summary>
        /// Gets the URL for a specific environment
        /// </summary>
        /// <param name="environment">The environment to get the URL for</param>
        /// <returns>The URL for the specified environment</returns>
        string GetEnvironmentUrl(ZatcaEnvironment environment);

        /// <summary>
        /// Gets the name of the current environment
        /// </summary>
        /// <returns>The environment name as a string</returns>
        string GetCurrentEnvironmentName();

        /// <summary>
        /// Validates that the ZATCA settings are properly configured
        /// </summary>
        /// <returns>True if settings are valid, false otherwise</returns>
        bool ValidateSettings();
    }
}
