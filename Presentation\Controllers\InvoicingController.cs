﻿using Application.Contracts.IServices;
using Application.Models.Zatca;
using Domain.Entities;
using Domain.Enums;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Presentation.Models;
using System.ComponentModel.DataAnnotations;

namespace Presentation.Controllers
{
    /// <summary>
    /// Controller for managing ZATCA invoice operations
    /// </summary>
    [Route("api/v{version:apiVersion}/[controller]")]
    [ApiController]
    [ApiVersion("1.0")]
    [Produces("application/json")]
    [Tags("Invoice Management")]
    public class InvoicingController : ControllerBase
    {
        private readonly IZatcaInvoiceSender _zatcaInvoiceSender;
        private readonly IZatcaEnvironmentService _zatcaEnvironmentService;
        private readonly ILogger<InvoicingController> _logger;

        /// <summary>
        /// Initializes a new instance of the InvoicingController
        /// </summary>
        /// <param name="zatcaInvoiceSender">Service for sending invoices to ZATCA</param>
        /// <param name="zatcaEnvironmentService">Service for managing ZATCA environments</param>
        /// <param name="logger">Logger instance</param>
        public InvoicingController(
            IZatcaInvoiceSender zatcaInvoiceSender,
            IZatcaEnvironmentService zatcaEnvironmentService,
            ILogger<InvoicingController> logger)
        {
            _zatcaInvoiceSender = zatcaInvoiceSender;
            _zatcaEnvironmentService = zatcaEnvironmentService;
            _logger = logger;
        }
        /// <summary>
        /// Submits an invoice to ZATCA for clearance/reporting in the specified environment
        /// </summary>
        /// <param name="invoice">The invoice data to be submitted to ZATCA</param>
        /// <param name="environment">The ZATCA environment to submit to (Developer, Simulation, or Production). Defaults to Simulation if not specified.</param>
        /// <returns>Result of the invoice submission including validation messages and status</returns>
        /// <response code="200">Invoice successfully submitted and processed</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="422">Invoice validation failed</response>
        /// <response code="500">Internal server error</response>
        [HttpPost]
        [ProducesResponseType(typeof(ApiResponse<InvoiceSubmissionResponse>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        [ProducesResponseType(typeof(ApiResponse), 422)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<IActionResult> SubmitInvoice(
            [FromBody] InvoiceToZatca invoice,
            [FromQuery] ZatcaEnvironment environment = ZatcaEnvironment.Simulation)
        {
            if (invoice == null)
            {
                _logger.LogWarning("Invoice submission failed: Null invoice received");
                return BadRequest(ApiResponse.ErrorResponse(
                    "Invoice data is required",
                    400,
                    new List<string> { "Invoice object cannot be null" }));
            }

            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values
                    .SelectMany(v => v.Errors)
                    .Select(e => e.ErrorMessage)
                    .ToList();

                _logger.LogWarning("Invoice submission failed: Validation errors - {Errors}", string.Join(", ", errors));
                return BadRequest(ApiResponse.ValidationErrorResponse(errors));
            }

            // Validate ZATCA settings
            if (!_zatcaEnvironmentService.ValidateSettings())
            {
                _logger.LogError("ZATCA settings validation failed");
                return StatusCode(500, ApiResponse.ErrorResponse(
                    "ZATCA configuration is invalid",
                    500,
                    new List<string> { "Please check ZATCA settings configuration" }));
            }

            try
            {
                var environmentName = environment.ToString();
                _logger.LogInformation("Submitting invoice {InvoiceId} to ZATCA {Environment} environment",
                    invoice.InvoiceId, environmentName);

                // Set the environment and get the corresponding URL
                var environmentUrl = _zatcaEnvironmentService.SetEnvironment(environment);

                _logger.LogInformation("Using ZATCA URL: {Url} for environment: {Environment}",
                    environmentUrl, environmentName);

                var zatcaResult = await _zatcaInvoiceSender.SendInvoiceToZatcaAsync(invoice);

                var response = MapToInvoiceSubmissionResponse(zatcaResult);

                if (zatcaResult.StatusCode == 200)
                {
                    _logger.LogInformation("Invoice {InvoiceId} successfully submitted to ZATCA {Environment} environment",
                        invoice.InvoiceId, environmentName);
                    return Ok(ApiResponse<InvoiceSubmissionResponse>.SuccessResponse(
                        response,
                        $"Invoice successfully submitted to ZATCA {environmentName} environment"));
                }
                else
                {
                    _logger.LogWarning("Invoice {InvoiceId} submission failed with status {StatusCode} in {Environment} environment",
                        invoice.InvoiceId, zatcaResult.StatusCode, environmentName);
                    return UnprocessableEntity(ApiResponse<InvoiceSubmissionResponse>.ErrorResponse(
                        zatcaResult.Message ?? $"Invoice submission failed in {environmentName} environment",
                        zatcaResult.StatusCode));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error submitting invoice {InvoiceId} to ZATCA {Environment} environment",
                    invoice.InvoiceId, environment);
                return StatusCode(500, ApiResponse.ErrorResponse(
                    "An error occurred while processing the invoice",
                    500,
                    new List<string> { ex.Message }));
            }
        }
        /// <summary>
        /// Gets the current ZATCA environment configuration
        /// </summary>
        /// <returns>Information about the current ZATCA environment</returns>
        /// <response code="200">Environment information retrieved successfully</response>
        [HttpGet("environment")]
        [ProducesResponseType(typeof(ApiResponse<ZatcaEnvironmentInfo>), 200)]
        public IActionResult GetEnvironmentInfo()
        {
            try
            {
                var currentEnvironment = _zatcaEnvironmentService.GetCurrentEnvironment();
                var currentUrl = _zatcaEnvironmentService.GetCurrentEnvironmentUrl();
                var environmentName = _zatcaEnvironmentService.GetCurrentEnvironmentName();

                var environmentInfo = new ZatcaEnvironmentInfo
                {
                    CurrentEnvironment = currentEnvironment,
                    CurrentEnvironmentName = environmentName,
                    CurrentUrl = currentUrl,
                    AvailableEnvironments = Enum.GetValues<ZatcaEnvironment>()
                        .Select(env => new EnvironmentOption
                        {
                            Environment = env,
                            Name = env.ToString(),
                            Url = _zatcaEnvironmentService.GetEnvironmentUrl(env)
                        }).ToList()
                };

                return Ok(ApiResponse<ZatcaEnvironmentInfo>.SuccessResponse(
                    environmentInfo,
                    "Environment information retrieved successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving ZATCA environment information");
                return StatusCode(500, ApiResponse.ErrorResponse(
                    "An error occurred while retrieving environment information",
                    500,
                    new List<string> { ex.Message }));
            }
        }

        /// <summary>
        /// Sets the ZATCA environment for subsequent operations
        /// </summary>
        /// <param name="environment">The environment to set</param>
        /// <returns>Confirmation of the environment change</returns>
        /// <response code="200">Environment set successfully</response>
        /// <response code="400">Invalid environment specified</response>
        /// <response code="500">Internal server error</response>
        [HttpPut("environment")]
        [ProducesResponseType(typeof(ApiResponse<ZatcaEnvironmentInfo>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public IActionResult SetEnvironment([FromBody] SetEnvironmentRequest request)
        {
            if (!Enum.IsDefined(typeof(ZatcaEnvironment), request.Environment))
            {
                return BadRequest(ApiResponse.ErrorResponse(
                    "Invalid environment specified",
                    400,
                    new List<string> { "Environment must be Developer, Simulation, or Production" }));
            }

            try
            {
                var url = _zatcaEnvironmentService.SetEnvironment(request.Environment);
                var environmentName = request.Environment.ToString();

                var environmentInfo = new ZatcaEnvironmentInfo
                {
                    CurrentEnvironment = request.Environment,
                    CurrentEnvironmentName = environmentName,
                    CurrentUrl = url,
                    AvailableEnvironments = Enum.GetValues<ZatcaEnvironment>()
                        .Select(env => new EnvironmentOption
                        {
                            Environment = env,
                            Name = env.ToString(),
                            Url = _zatcaEnvironmentService.GetEnvironmentUrl(env)
                        }).ToList()
                };

                _logger.LogInformation("ZATCA environment changed to {Environment} with URL: {Url}",
                    environmentName, url);

                return Ok(ApiResponse<ZatcaEnvironmentInfo>.SuccessResponse(
                    environmentInfo,
                    $"Environment successfully set to {environmentName}"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting ZATCA environment to {Environment}", request.Environment);
                return StatusCode(500, ApiResponse.ErrorResponse(
                    "An error occurred while setting the environment",
                    500,
                    new List<string> { ex.Message }));
            }
        }

        /// <summary>
        /// Maps ZATCA result to standardized response model
        /// </summary>
        private static InvoiceSubmissionResponse MapToInvoiceSubmissionResponse(ZatcaInvoiceResultResponse zatcaResult)
        {
            return new InvoiceSubmissionResponse
            {
                InvoiceHash = zatcaResult.InvoiceHash ?? string.Empty,
                ReportedToZatca = zatcaResult.ReportedToZatca,
                SubmissionDate = DateTime.UtcNow,
                WarningMessages = zatcaResult.WarningMessages?.Select(w => new ValidationMessage
                {
                    Type = w.Type,
                    Code = w.Code,
                    Category = w.Category,
                    Message = w.Message,
                    Status = w.Status
                }).ToList() ?? new List<ValidationMessage>(),
                ErrorMessages = zatcaResult.ErrorMessages?.Select(e => new ValidationMessage
                {
                    Type = e.Type,
                    Code = e.Code,
                    Category = e.Category,
                    Message = e.Message,
                    Status = e.Status
                }).ToList() ?? new List<ValidationMessage>()
            };
        }

    }
}
