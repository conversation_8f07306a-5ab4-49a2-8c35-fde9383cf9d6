using Domain.Enums;
using System.ComponentModel.DataAnnotations;

namespace Presentation.Models
{
    /// <summary>
    /// Information about the current ZATCA environment configuration
    /// </summary>
    public class ZatcaEnvironmentInfo
    {
        /// <summary>
        /// The currently selected ZATCA environment
        /// </summary>
        public ZatcaEnvironment CurrentEnvironment { get; set; }

        /// <summary>
        /// The name of the current environment
        /// </summary>
        public string CurrentEnvironmentName { get; set; } = string.Empty;

        /// <summary>
        /// The URL for the current environment
        /// </summary>
        public string CurrentUrl { get; set; } = string.Empty;

        /// <summary>
        /// List of all available environments with their details
        /// </summary>
        public List<EnvironmentOption> AvailableEnvironments { get; set; } = new();
    }

    /// <summary>
    /// Represents an available ZATCA environment option
    /// </summary>
    public class EnvironmentOption
    {
        /// <summary>
        /// The environment enum value
        /// </summary>
        public ZatcaEnvironment Environment { get; set; }

        /// <summary>
        /// The display name of the environment
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// The URL for this environment
        /// </summary>
        public string Url { get; set; } = string.Empty;
    }

    /// <summary>
    /// Request model for setting the ZATCA environment
    /// </summary>
    public class SetEnvironmentRequest
    {
        /// <summary>
        /// The environment to set (Developer, Simulation, or Production)
        /// </summary>
        [Required]
        public ZatcaEnvironment Environment { get; set; }
    }
}
