@inject SignInManager<ApplicationUser> SignInManager
@{
    var hasExternalLogins = (await SignInManager.GetExternalAuthenticationSchemesAsync()).Any();
}

<div class="modern-card" style="padding: 1.5rem;">
    <div style="margin-bottom: 1.5rem; border-bottom: 1px solid var(--color-gray-200); padding-bottom: 1rem;">
        <h3 style="font-size: 1.125rem; font-weight: 600; color: var(--color-gray-900); margin-bottom: 0.5rem;">Account Settings</h3>
        <p style="font-size: 0.875rem; color: var(--color-gray-600);">Manage your account preferences</p>
    </div>

    <nav style="display: flex; flex-direction: column; gap: 0.25rem;">
        <a style="display: flex; align-items: center; padding: 0.75rem 1rem; border-radius: var(--radius-lg); text-decoration: none; color: var(--color-gray-700); transition: all var(--transition-fast); @(ManageNavPages.IndexNavClass(ViewContext) == "active" ? "background: var(--color-primary-50); color: var(--color-primary-700); font-weight: 500;" : "")"
           id="profile"
           asp-page="./Index">
            <i class="bi bi-person" style="margin-right: 0.75rem;"></i>
            <span>Profile</span>
        </a>

        <a style="display: flex; align-items: center; padding: 0.75rem 1rem; border-radius: var(--radius-lg); text-decoration: none; color: var(--color-gray-700); transition: all var(--transition-fast); @(ManageNavPages.ChangePasswordNavClass(ViewContext) == "active" ? "background: var(--color-primary-50); color: var(--color-primary-700); font-weight: 500;" : "")"
           id="change-password"
           asp-page="./ChangePassword">
            <i class="bi bi-key" style="margin-right: 0.75rem;"></i>
            <span>Password</span>
        </a>

        @if (hasExternalLogins)
        {
            <a style="display: flex; align-items: center; padding: 0.75rem 1rem; border-radius: var(--radius-lg); text-decoration: none; color: var(--color-gray-700); transition: all var(--transition-fast); @(ManageNavPages.ExternalLoginsNavClass(ViewContext) == "active" ? "background: var(--color-primary-50); color: var(--color-primary-700); font-weight: 500;" : "")"
               id="external-login"
               asp-page="./ExternalLogins">
                <i class="bi bi-link-45deg" style="margin-right: 0.75rem;"></i>
                <span>External logins</span>
            </a>
        }

        <a style="display: flex; align-items: center; padding: 0.75rem 1rem; border-radius: var(--radius-lg); text-decoration: none; color: var(--color-gray-700); transition: all var(--transition-fast); @(ManageNavPages.TwoFactorAuthenticationNavClass(ViewContext) == "active" ? "background: var(--color-primary-50); color: var(--color-primary-700); font-weight: 500;" : "")"
           id="two-factor"
           asp-page="./TwoFactorAuthentication">
            <i class="bi bi-shield-check" style="margin-right: 0.75rem;"></i>
            <span>Two-factor authentication</span>
        </a>

        <a style="display: flex; align-items: center; padding: 0.75rem 1rem; border-radius: var(--radius-lg); text-decoration: none; color: var(--color-gray-700); transition: all var(--transition-fast); @(ManageNavPages.PersonalDataNavClass(ViewContext) == "active" ? "background: var(--color-primary-50); color: var(--color-primary-700); font-weight: 500;" : "")"
           id="personal-data"
           asp-page="./PersonalData">
            <i class="bi bi-file-person" style="margin-right: 0.75rem;"></i>
            <span>Personal data</span>
        </a>
    </nav>
</div>
