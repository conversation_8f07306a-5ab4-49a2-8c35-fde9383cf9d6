using Newtonsoft.Json;
using System.Net;
using System.Text;
using ZatcaWebMvc.Models;

namespace ZatcaWebMvc.Services
{
    /// <summary>
    /// Enhanced service for communicating with ZATCA API with authentication and error handling
    /// </summary>
    public class ZatcaApiService : IZatcaApiService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<ZatcaApiService> _logger;
        private readonly IConfiguration _configuration;
        private readonly string _baseUrl;
        private readonly string? _apiKey;
        private readonly int _timeoutSeconds;

        public ZatcaApiService(HttpClient httpClient, ILogger<ZatcaApiService> logger, IConfiguration configuration)
        {
            _httpClient = httpClient;
            _logger = logger;
            _configuration = configuration;
            _baseUrl = configuration["ZatcaApi:BaseUrl"] ?? "http://localhost:5147";
            _apiKey = configuration["ZatcaApi:ApiKey"];
            _timeoutSeconds = configuration.GetValue<int>("ZatcaApi:TimeoutSeconds", 30);

            ConfigureHttpClient();
        }

        /// <summary>
        /// Configure HTTP client with authentication and default settings
        /// </summary>
        private void ConfigureHttpClient()
        {
            _httpClient.BaseAddress = new Uri(_baseUrl);
            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Add("Accept", "application/json");
            _httpClient.DefaultRequestHeaders.Add("User-Agent", "ZATCA-MVC-Client/1.0");
            _httpClient.Timeout = TimeSpan.FromSeconds(_timeoutSeconds);

            // Add API key if configured
            if (!string.IsNullOrEmpty(_apiKey))
            {
                _httpClient.DefaultRequestHeaders.Add("X-API-Key", _apiKey);
            }

            _logger.LogInformation("ZATCA API Service configured with base URL: {BaseUrl}", _baseUrl);
        }

        #region Private Helper Methods

        /// <summary>
        /// Handle HTTP response and create standardized API response
        /// </summary>
        private async Task<ApiResponse<T>> HandleResponseAsync<T>(HttpResponseMessage response, string operation)
        {
            var responseContent = await response.Content.ReadAsStringAsync();

            try
            {
                if (response.IsSuccessStatusCode)
                {
                    // Debug logging for environment operations
                    if (operation == "GetCurrentEnvironment")
                    {
                        _logger.LogInformation("Raw API response for {Operation}: {Response}", operation, responseContent);
                    }

                    var result = JsonConvert.DeserializeObject<ApiResponse<T>>(responseContent);
                    if (result != null)
                    {
                        // Debug logging for environment operations
                        if (operation == "GetCurrentEnvironment")
                        {
                            _logger.LogInformation("Deserialized result for {Operation}: Success={Success}, Data={Data}",
                                operation, result.Success, result.Data);
                        }

                        _logger.LogInformation("API operation {Operation} completed successfully", operation);
                        return result;
                    }

                    // If direct deserialization fails, try to wrap the response
                    if (operation == "GetCurrentEnvironment")
                    {
                        _logger.LogInformation("First deserialization failed for {Operation}, trying fallback approach", operation);
                    }

                    var data = JsonConvert.DeserializeObject<T>(responseContent);

                    if (operation == "GetCurrentEnvironment")
                    {
                        _logger.LogInformation("Fallback deserialization result for {Operation}: {Data}", operation, data);
                    }

                    return new ApiResponse<T>
                    {
                        Success = true,
                        Data = data,
                        Message = "Operation completed successfully",
                        StatusCode = (int)response.StatusCode
                    };
                }

                // Handle error responses
                var errorResponse = new ApiResponse<T>
                {
                    Success = false,
                    Message = GetErrorMessage(response.StatusCode, operation),
                    StatusCode = (int)response.StatusCode,
                    Errors = new List<string>()
                };

                // Try to extract error details from response
                try
                {
                    var errorData = JsonConvert.DeserializeObject<ApiResponse>(responseContent);
                    if (errorData?.Errors?.Any() == true)
                    {
                        errorResponse.Errors = errorData.Errors;
                    }
                    if (!string.IsNullOrEmpty(errorData?.Message))
                    {
                        errorResponse.Message = errorData.Message;
                    }
                }
                catch
                {
                    errorResponse.Errors.Add(responseContent);
                }

                _logger.LogWarning("API operation {Operation} failed with status {StatusCode}: {Message}",
                    operation, response.StatusCode, errorResponse.Message);

                return errorResponse;
            }
            catch (JsonException ex)
            {
                _logger.LogError(ex, "Failed to deserialize API response for operation {Operation}", operation);
                return new ApiResponse<T>
                {
                    Success = false,
                    Message = "Failed to process API response",
                    StatusCode = (int)response.StatusCode,
                    Errors = new List<string> { ex.Message, responseContent }
                };
            }
        }

        /// <summary>
        /// Get user-friendly error message based on HTTP status code
        /// </summary>
        private static string GetErrorMessage(HttpStatusCode statusCode, string operation)
        {
            return statusCode switch
            {
                HttpStatusCode.Unauthorized => "Authentication failed. Please check your API credentials.",
                HttpStatusCode.Forbidden => "Access denied. You don't have permission to perform this operation.",
                HttpStatusCode.NotFound => $"The requested resource for {operation} was not found.",
                HttpStatusCode.BadRequest => "Invalid request data. Please check your input and try again.",
                HttpStatusCode.InternalServerError => "Server error occurred. Please try again later.",
                HttpStatusCode.ServiceUnavailable => "ZATCA API service is temporarily unavailable.",
                HttpStatusCode.RequestTimeout => "Request timed out. Please try again.",
                _ => $"API operation {operation} failed with status {statusCode}"
            };
        }

        /// <summary>
        /// Create HTTP content from object
        /// </summary>
        private static StringContent CreateJsonContent(object data)
        {
            var json = JsonConvert.SerializeObject(data, new JsonSerializerSettings
            {
                NullValueHandling = NullValueHandling.Ignore,
                DateFormatHandling = DateFormatHandling.IsoDateFormat
            });
            return new StringContent(json, Encoding.UTF8, "application/json");
        }

        /// <summary>
        /// Execute API operation with comprehensive error handling
        /// </summary>
        private async Task<ApiResponse<T>> ExecuteApiOperationAsync<T>(
            Func<Task<HttpResponseMessage>> operation,
            string operationName)
        {
            try
            {
                _logger.LogInformation("Executing API operation: {Operation}", operationName);
                var response = await operation();
                return await HandleResponseAsync<T>(response, operationName);
            }
            catch (HttpRequestException ex)
            {
                _logger.LogError(ex, "HTTP request failed for operation {Operation}", operationName);
                return new ApiResponse<T>
                {
                    Success = false,
                    Message = "Network error occurred. Please check your connection and try again.",
                    Errors = new List<string> { ex.Message }
                };
            }
            catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException)
            {
                _logger.LogError(ex, "Request timeout for operation {Operation}", operationName);
                return new ApiResponse<T>
                {
                    Success = false,
                    Message = "Request timed out. Please try again.",
                    Errors = new List<string> { "Operation timed out" }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error in operation {Operation}", operationName);
                return new ApiResponse<T>
                {
                    Success = false,
                    Message = "An unexpected error occurred. Please try again.",
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        #endregion

        #region Invoice Operations

        public async Task<ApiResponse<InvoiceSubmissionResponse>> CreateInvoiceAsync(InvoiceCreateRequest request)
        {
            if (request == null)
            {
                return new ApiResponse<InvoiceSubmissionResponse>
                {
                    Success = false,
                    Message = "Invoice request cannot be null",
                    Errors = new List<string> { "Invalid request data" }
                };
            }

            return await ExecuteApiOperationAsync<InvoiceSubmissionResponse>(
                async () =>
                {
                    var content = CreateJsonContent(request);
                    return await _httpClient.PostAsync("/api/v1/Invoicing", content);
                },
                "CreateInvoice"
            );
        }

        public async Task<ApiResponse<InvoiceSubmissionResponse>> GetInvoiceAsync(int id)
        {
            if (id <= 0)
            {
                return new ApiResponse<InvoiceSubmissionResponse>
                {
                    Success = false,
                    Message = "Invalid invoice ID",
                    Errors = new List<string> { "Invoice ID must be greater than 0" }
                };
            }

            return await ExecuteApiOperationAsync<InvoiceSubmissionResponse>(
                async () => await _httpClient.GetAsync($"/api/v1/Invoicing/{id}"),
                $"GetInvoice-{id}"
            );
        }

        public async Task<ApiResponse<List<InvoiceSubmissionResponse>>> GetAllInvoicesAsync()
        {
            return await ExecuteApiOperationAsync<List<InvoiceSubmissionResponse>>(
                async () => await _httpClient.GetAsync("/api/v1/Invoicing"),
                "GetAllInvoices"
            );
        }

        #endregion

        #region Certificate Operations

        public async Task<ApiResponse<CertificateCreationResponse>> CreateCertificateAsync(CertificateCreateRequest request)
        {
            try
            {
                // Convert CertificateCreateRequest to ZatcaCsrCreationRequestDto (API expected format)
                var zatcaCsrRequest = new
                {
                    BusinessCategory = request.BusinessCategory,
                    InvoiceType = request.InvoiceType,
                    LocationAddress = request.LocationAddress,
                    VATNumber = request.VATNumber,
                    CountryName = request.CountryName,
                    BranchName = request.BranchName,
                    OrganizationName = request.OrganizationName,
                    Email = request.Email,
                    IsProduction = request.IsProduction
                };

                var json = JsonConvert.SerializeObject(zatcaCsrRequest);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                // Add OTP as query parameter
                var url = $"/api/v1/Certificate?otp={Uri.EscapeDataString(request.Otp)}";
                var response = await _httpClient.PostAsync(url, content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject<ApiResponse<CertificateCreationResponse>>(responseContent);
                    return result ?? new ApiResponse<CertificateCreationResponse> { Success = false, Message = "Failed to deserialize response" };
                }

                return new ApiResponse<CertificateCreationResponse>
                {
                    Success = false,
                    Message = $"API call failed with status {response.StatusCode}",
                    StatusCode = (int)response.StatusCode,
                    Errors = new List<string> { responseContent }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating certificate");
                return new ApiResponse<CertificateCreationResponse>
                {
                    Success = false,
                    Message = "An error occurred while creating the certificate",
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        public async Task<ApiResponse<List<CertificateCreationResponse>>> GetAllCertificatesAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync("/api/v1/Certificate");
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject<ApiResponse<List<CertificateCreationResponse>>>(responseContent);
                    return result ?? new ApiResponse<List<CertificateCreationResponse>> { Success = false, Message = "Failed to deserialize response" };
                }
                
                return new ApiResponse<List<CertificateCreationResponse>>
                {
                    Success = false,
                    Message = $"API call failed with status {response.StatusCode}",
                    StatusCode = (int)response.StatusCode
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all certificates");
                return new ApiResponse<List<CertificateCreationResponse>>
                {
                    Success = false,
                    Message = "An error occurred while retrieving certificates",
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        public async Task<ApiResponse<CertificateCreationResponse>> GetCertificateAsync(int id)
        {
            try
            {
                var response = await _httpClient.GetAsync($"/api/v1/Certificate/{id}");
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject<ApiResponse<CertificateCreationResponse>>(responseContent);
                    return result ?? new ApiResponse<CertificateCreationResponse> { Success = false, Message = "Failed to deserialize response" };
                }
                
                return new ApiResponse<CertificateCreationResponse>
                {
                    Success = false,
                    Message = $"API call failed with status {response.StatusCode}",
                    StatusCode = (int)response.StatusCode
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting certificate {CertificateId}", id);
                return new ApiResponse<CertificateCreationResponse>
                {
                    Success = false,
                    Message = "An error occurred while retrieving the certificate",
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        #endregion

        #region Seller Operations

        public async Task<ApiResponse<SellerResponse>> CreateSellerAsync(SellerCreateRequest request)
        {
            try
            {
                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                
                var response = await _httpClient.PostAsync("/api/v1/Seller", content);
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject<ApiResponse<SellerResponse>>(responseContent);
                    return result ?? new ApiResponse<SellerResponse> { Success = false, Message = "Failed to deserialize response" };
                }
                
                return new ApiResponse<SellerResponse>
                {
                    Success = false,
                    Message = $"API call failed with status {response.StatusCode}",
                    StatusCode = (int)response.StatusCode,
                    Errors = new List<string> { responseContent }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating seller");
                return new ApiResponse<SellerResponse>
                {
                    Success = false,
                    Message = "An error occurred while creating the seller",
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        public async Task<ApiResponse<List<SellerResponse>>> GetAllSellersAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync("/api/v1/Seller");
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    // API returns single seller, convert to list for frontend compatibility
                    var singleSellerResult = JsonConvert.DeserializeObject<ApiResponse<SellerResponse>>(responseContent);

                    if (singleSellerResult?.Success == true && singleSellerResult.Data != null)
                    {
                        return new ApiResponse<List<SellerResponse>>
                        {
                            Success = true,
                            Message = singleSellerResult.Message,
                            Data = new List<SellerResponse> { singleSellerResult.Data },
                            StatusCode = singleSellerResult.StatusCode
                        };
                    }
                    else if (singleSellerResult?.Success == false)
                    {
                        // No seller found (404) - return empty list
                        return new ApiResponse<List<SellerResponse>>
                        {
                            Success = true,
                            Message = "No sellers found",
                            Data = new List<SellerResponse>(),
                            StatusCode = 200
                        };
                    }

                    return new ApiResponse<List<SellerResponse>> { Success = false, Message = "Failed to deserialize response" };
                }

                return new ApiResponse<List<SellerResponse>>
                {
                    Success = false,
                    Message = $"API call failed with status {response.StatusCode}",
                    StatusCode = (int)response.StatusCode
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all sellers");
                return new ApiResponse<List<SellerResponse>>
                {
                    Success = false,
                    Message = "An error occurred while retrieving sellers",
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        public async Task<ApiResponse<SellerResponse>> GetSellerAsync(int id)
        {
            try
            {
                // API only supports single seller, ignore ID parameter
                var response = await _httpClient.GetAsync("/api/v1/Seller");
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject<ApiResponse<SellerResponse>>(responseContent);
                    return result ?? new ApiResponse<SellerResponse> { Success = false, Message = "Failed to deserialize response" };
                }

                return new ApiResponse<SellerResponse>
                {
                    Success = false,
                    Message = $"API call failed with status {response.StatusCode}",
                    StatusCode = (int)response.StatusCode
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting seller");
                return new ApiResponse<SellerResponse>
                {
                    Success = false,
                    Message = "An error occurred while retrieving the seller",
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        public async Task<ApiResponse> UpdateSellerAsync(int id, SellerCreateRequest request)
        {
            // API doesn't support updating sellers - return not supported error
            _logger.LogWarning("Update seller operation is not supported by the API");
            return new ApiResponse
            {
                Success = false,
                Message = "Updating seller information is not supported. Please delete and recreate the seller if changes are needed.",
                StatusCode = 405, // Method Not Allowed
                Errors = new List<string> { "Update operation not supported by ZATCA API" }
            };
        }

        public async Task<ApiResponse> DeleteSellerAsync(int id)
        {
            try
            {
                // API doesn't use ID parameter for delete, ignore it
                var response = await _httpClient.DeleteAsync("/api/v1/Seller");
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject<ApiResponse>(responseContent);
                    return result ?? new ApiResponse { Success = false, Message = "Failed to deserialize response" };
                }

                return new ApiResponse
                {
                    Success = false,
                    Message = $"API call failed with status {response.StatusCode}",
                    StatusCode = (int)response.StatusCode
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting seller");
                return new ApiResponse
                {
                    Success = false,
                    Message = "An error occurred while deleting the seller",
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        #endregion

        #region Health and Diagnostics

        public async Task<bool> IsApiHealthyAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync("/swagger/index.html");
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<ApiResponse<ApiHealthStatus>> GetApiHealthStatusAsync()
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            try
            {
                var response = await _httpClient.GetAsync("/swagger/index.html");
                stopwatch.Stop();

                var healthStatus = new ApiHealthStatus
                {
                    IsHealthy = response.IsSuccessStatusCode,
                    Status = response.IsSuccessStatusCode ? "Healthy" : "Unhealthy",
                    CheckTime = DateTime.UtcNow,
                    ResponseTime = stopwatch.Elapsed,
                    Version = "1.0",
                    Details = new Dictionary<string, object>
                    {
                        ["BaseUrl"] = _baseUrl,
                        ["StatusCode"] = (int)response.StatusCode,
                        ["HasApiKey"] = !string.IsNullOrEmpty(_apiKey)
                    }
                };

                return new ApiResponse<ApiHealthStatus>
                {
                    Success = true,
                    Data = healthStatus,
                    Message = "Health check completed"
                };
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Health check failed");

                var healthStatus = new ApiHealthStatus
                {
                    IsHealthy = false,
                    Status = "Error",
                    CheckTime = DateTime.UtcNow,
                    ResponseTime = stopwatch.Elapsed,
                    Version = "1.0",
                    Details = new Dictionary<string, object>
                    {
                        ["BaseUrl"] = _baseUrl,
                        ["Error"] = ex.Message,
                        ["HasApiKey"] = !string.IsNullOrEmpty(_apiKey)
                    }
                };

                return new ApiResponse<ApiHealthStatus>
                {
                    Success = false,
                    Data = healthStatus,
                    Message = "Health check failed",
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        public async Task<ApiResponse> TestAuthenticationAsync()
        {
            var result = await ExecuteApiOperationAsync<object>(
                async () => await _httpClient.GetAsync("/api/v1/health"),
                "TestAuthentication"
            );

            return new ApiResponse
            {
                Success = result.Success,
                Message = result.Message,
                Errors = result.Errors
            };
        }

        #endregion

        #region Enhanced Invoice Operations

        public async Task<ApiResponse<List<InvoiceSubmissionResponse>>> GetInvoicesAsync(int page = 1, int pageSize = 10)
        {
            if (page <= 0 || pageSize <= 0)
            {
                return new ApiResponse<List<InvoiceSubmissionResponse>>
                {
                    Success = false,
                    Message = "Invalid pagination parameters",
                    Errors = new List<string> { "Page and pageSize must be greater than 0" }
                };
            }

            return await ExecuteApiOperationAsync<List<InvoiceSubmissionResponse>>(
                async () => await _httpClient.GetAsync($"/api/v1/Invoicing?page={page}&pageSize={pageSize}"),
                $"GetInvoices-Page{page}"
            );
        }

        public async Task<ApiResponse<InvoiceSubmissionResponse>> SubmitInvoiceAsync(int invoiceId)
        {
            if (invoiceId <= 0)
            {
                return new ApiResponse<InvoiceSubmissionResponse>
                {
                    Success = false,
                    Message = "Invalid invoice ID",
                    Errors = new List<string> { "Invoice ID must be greater than 0" }
                };
            }

            return await ExecuteApiOperationAsync<InvoiceSubmissionResponse>(
                async () => await _httpClient.PostAsync($"/api/v1/Invoicing/{invoiceId}/submit", null),
                $"SubmitInvoice-{invoiceId}"
            );
        }

        #endregion

        #region Enhanced Certificate Operations

        public async Task<ApiResponse<CertificateCreationResponse>> RenewCertificateAsync(int certificateId)
        {
            if (certificateId <= 0)
            {
                return new ApiResponse<CertificateCreationResponse>
                {
                    Success = false,
                    Message = "Invalid certificate ID",
                    Errors = new List<string> { "Certificate ID must be greater than 0" }
                };
            }

            return await ExecuteApiOperationAsync<CertificateCreationResponse>(
                async () => await _httpClient.PostAsync($"/api/v1/Certificate/{certificateId}/renew", null),
                $"RenewCertificate-{certificateId}"
            );
        }

        #endregion

        #region Environment Management

        /// <summary>
        /// Get current ZATCA environment
        /// </summary>
        public async Task<ApiResponse<EnvironmentResponse>> GetCurrentEnvironmentAsync()
        {
            return await ExecuteApiOperationAsync<EnvironmentResponse>(
                async () => await _httpClient.GetAsync("/api/v1.0/Invoicing/environment"),
                "GetCurrentEnvironment"
            );
        }

        /// <summary>
        /// Set ZATCA environment
        /// </summary>
        public async Task<ApiResponse<EnvironmentResponse>> SetEnvironmentAsync(SetEnvironmentRequest request)
        {
            var json = JsonConvert.SerializeObject(request);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            return await ExecuteApiOperationAsync<EnvironmentResponse>(
                async () => await _httpClient.PutAsync("/api/v1.0/Invoicing/environment", content),
                "SetEnvironment"
            );
        }

        #endregion
    }
}
